---
title: Time-based checks
description: <PERSON> temporary access by attaching expiration times to role assignments
sidebarTitle: "Time-based checks"
---

Time-based checks let you give someone access for a limited time by adding an expiration to their role or permission.

## Implementation

The `has_role_with_expiry` fact is like the usual `has_role` fact, but the fourth argument is the expiration time, expressed as an integer representing seconds since the Unix epoch.


<Warning>**Note:** On a *nix system, `date +%s` will give you the current Unix time in seconds.</Warning>

`@current_unix_time` is a constant you can use in rules that will resolve to the current Unix time at the time your query is evaluated.

To implement access expiration, we compare the expiration time with the current time.

```polar
actor User { }

resource Repository {
  roles = ["member"];
  permissions = ["read"];

  "read" if "member";
}

has_role(actor: Actor, role: String, repo: Repository) if
  expiration matches Integer and
  has_role_with_expiry(actor, role, repo, expiration) and
  expiration > @current_unix_time;

test "access to repositories is conditional on expiry" {
  setup {
    # Alice's access expires in 2033
    has_role_with_expiry(User{"alice"}, "member", Repository{"anvil"}, 2002913298);
    # <PERSON>'s access expired in 2022
    has_role_with_expiry(User{"bob"}, "member", Repository{"anvil"}, 1655758135);
  }

  assert allow(User{"alice"}, "read", Repository{"anvil"});
  assert_not allow(User{"bob"}, "read", Repository{"anvil"});
}
```
