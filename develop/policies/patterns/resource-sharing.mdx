---
title: The Resource Sharing Pattern
description: Use this pattern to grant access on a resource to a specific user
sidebarTitle: "Resource sharing"
---
Resource sharing grants users access to specific resources by assigning them roles, with additional permissions controlling who can share those resources with others.

## Implementation

First, define roles on the target resource. Then, add an "invite" permission to control which users can share the resource with others.

```polar
actor User { }

resource Repository {
  roles = ["reader", "admin"];
  permissions = ["read", "invite"];

  "read" if "reader";
  "invite" if "admin";
}
```

To share a resource, your application should follow this two-step process:

1. **Authorization check**: Verify the user has the `invite` permission on the resource
2. **Grant access**: Insert a `has_role` fact to assign the appropriate role to the invited user

For example, when <PERSON> (an admin) wants to share a repository with <PERSON> as a reader:
1. Check: `authorize(alice, "invite", repository)` 
2. Grant: Insert `has_role(bob, "reader", repository)`
