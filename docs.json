{"$schema": "https://mintlify.com/docs.json", "theme": "maple", "name": "Oso Cloud Documentation", "colors": {"primary": "#392396", "light": "#008ae6", "dark": "#392396"}, "favicon": "/favicon.png", "navigation": {"tabs": [{"tab": "Docs", "groups": [{"group": "Get Started", "pages": ["get-started/introduction", "get-started/quickstart-ui"]}, {"group": "Develop", "pages": [{"group": "Local Development", "pages": ["develop/local-dev/env-setup", "develop/local-dev/oso-dev-server", "develop/local-dev/oso-migrate"]}, {"group": "Write Policies", "pages": ["develop/policies/overview", "develop/policies/rbac", "develop/policies/rebac", "develop/policies/abac", "develop/policies/fga", "develop/policies/field-level-authorization", {"group": "Patterns", "pages": ["develop/policies/patterns/resource-creation", "develop/policies/patterns/conditional-roles", "develop/policies/patterns/custom-roles", "develop/policies/patterns/entitlements", "develop/policies/patterns/fields-as-resources", "develop/policies/patterns/fields-in-permissions", "develop/policies/patterns/impersonation", "develop/policies/patterns/organizational-hierarchy", "develop/policies/patterns/resource-sharing", "develop/policies/patterns/time-based-checks", "develop/policies/patterns/user-groups", "develop/policies/patterns/user-resource-relations"]}]}, {"group": "Manage Facts", "pages": ["develop/facts/overview", "develop/facts/insert-facts", "develop/facts/update-facts", "develop/facts/sync-facts", "develop/facts/export-facts", "develop/facts/context-facts"]}, {"group": "Enforce Permissions", "pages": ["develop/enforce/authorize-requests", "develop/enforce/enforcement-strategies", "develop/enforce/query-facts", "develop/enforce/list-filtering"]}, {"group": "Troubleshooting", "pages": ["develop/troubleshooting/logs", "develop/troubleshooting/explain", "develop/troubleshooting/policy-debugger", "develop/troubleshooting/query-performance"]}]}, {"group": "Deploy and Manage", "pages": ["deploy/deployment-models", "deploy/fallback-nodes", "deploy/backups-and-recovery", "deploy/migrations", "deploy/ci-cd", "deploy/security", {"group": "Account Management", "pages": ["deploy/account-management/configuration", "deploy/account-management/sso"]}]}]}, {"tab": "Reference", "groups": [{"group": "Polar", "pages": ["reference/polar/introduction", "reference/polar/rules-and-facts", "reference/polar/operators", "reference/polar/constants", "reference/polar/resource-blocks", "reference/polar/extends", "reference/polar/tests", "reference/polar/types"]}, {"group": "Oso API", "pages": [{"group": "Policies", "pages": ["reference/api/policy/get-policy", "reference/api/policy/get-policy_metadata", "reference/api/policy/post-policy"]}, {"group": "Facts", "pages": ["reference/api/centralized-authorization-data/post-facts", "reference/api/centralized-authorization-data/post-batch", "reference/api/centralized-authorization-data/post-bulk", "reference/api/centralized-authorization-data/post-bulk_load", "reference/api/centralized-authorization-data/post-bulk_delete", "reference/api/centralized-authorization-data/post-clear_data", "reference/api/centralized-authorization-data/delete-facts"]}, {"group": "Authorization Check", "pages": ["reference/api/check-api/post-actions", "reference/api/check-api/post-authorize_resources", "reference/api/check-api/post-authorize", "reference/api/check-api/post-evaluate_query", "reference/api/check-api/post-list", "reference/api/check-api/post-query"]}, {"group": "Local Authorization Check", "pages": ["reference/api/local-check-api/post-actions_query", "reference/api/local-check-api/post-authorize_query", "reference/api/local-check-api/post-evaluate_query_local", "reference/api/local-check-api/post-list_query"]}]}, {"group": "Client Libraries", "pages": ["reference/client-libraries/overview", "reference/client-libraries/install", "reference/client-libraries/policies", "reference/client-libraries/facts", "reference/client-libraries/authorization-checks", "reference/client-libraries/local-authorization", "reference/client-libraries/migration-guide", {"group": "Changelog", "pages": ["reference/sdks/changelog/cli", "reference/sdks/changelog/oso-dev-server", "reference/sdks/changelog/fallback-node", {"group": "SDKs", "pages": ["reference/sdks/changelog/python", "reference/sdks/changelog/javascript", "reference/sdks/changelog/go", "reference/sdks/changelog/java", "reference/sdks/changelog/ruby", "reference/sdks/changelog/dotnet", "reference/sdks/changelog/vscode-extension"]}]}]}]}, {"tab": "Learn", "groups": [{"group": "Tutorials", "pages": ["learn/tutorials/workflow-walkthrough", "learn/tutorials/gitcloud"]}, {"group": "Guides", "pages": ["learn/guides/adopt-local-authorization", "learn/guides/map-relational-data-to-facts", "learn/guides/ui", "learn/guides/authentication"]}]}]}, "logo": {"light": "/logo/logo-bear-black.png", "dark": "/logo/logo-bear-white.png"}, "navbar": {"links": [{"label": "Log in / Sign up", "href": "https://ui.osohq.com/", "icon": "user"}, {"label": "Meet an Eng", "href": "https://www.osohq.com/meet-oso", "icon": "video"}, {"label": "Status Page", "href": "https://oso.statuspage.io/", "icon": "wifi"}]}, "footer": {"socials": {"x": "https://x.com/osohq", "github": "https://github.com/osohq", "linkedin": "https://www.linkedin.com/company/osohq/", "slack": "https://join-slack.osohq.com/?_gl=1*1p3trmq*_gcl_au*MTQ1Njk1NTkwNC4xNzQ4NTYzMjYy"}}, "api": {"playground": {"display": "interactive"}, "examples": {"languages": ["curl", "python", "javascript"], "defaults": "required"}}, "contextual": {"options": ["copy", "chatgpt", "claude"]}}