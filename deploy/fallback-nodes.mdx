---
title: Oso Fallback Nodes
description: "Read-only instances for hybrid deployments"
sidebarTitle: "Fallback nodes"
---

To ensure high availability, Oso provides Fallback nodes—read-only replicas of the authorization service that run in your infrastructure, allowing your application to continue enforcing authorization even if Oso Cloud is unavailable.

<Warning>
Oso Fallback nodes are read-only instances that receive periodic updates with your environment’s rules and data. They may be slightly out of sync with Oso Cloud between updates, so they’re designed as a fallback mechanism for authorization checks—not a replacement for regular calls to Oso Cloud.
</Warning>

## Why use Oso Fallback?

Oso Cloud is built for high availability (up to 99.99% uptime), with edge nodes distributed across multiple regions and availability zones. It’s designed to withstand zone and regional outages without customer-visible impact. This infrastructure—along with the associated high availability strategy, monitoring, and operations—is managed by Oso so you don’t have to build it yourself.

However, service partitions and network disruptions can still isolate your application from Oso Cloud, and even the most resilient applications can experience unavailability. Since authorization is often on the critical path, Oso Fallback provides a local, read-only node that ensures authorization checks can continue when Oso Cloud is temporarily unreachable.

**Note:** This feature is only available for Growth plan customers.

## Set up Oso Fallback

Oso Fallback is deployed within your infrastructure and accessed by applications integrated with Oso Cloud. A typical deployment consists of one or more independent Oso Fallback nodes behind a load balancer, enabling horizontal scaling under load.

Since nodes operate independently and may be up to one minute out of sync, we recommend using load balancers with session affinity to ensure clients consistently communicate with the same node and avoid inconsistent authorization responses.

### System requirements

The minimum requirements for running the Oso Fallback node are:

- 1 CPU
- 256MB RAM
- 3x the disk space required for your environment

#### How much disk space do I need?

The total size of an environment is available on the [Fallback status page](https://ui.osohq.com/settings/?tab=fallback), which you can use to determine how much disk to allocate. Oso recommends 3× the current size to allow for growth and to ensure enough space to fetch the latest copy of rules and data.

#### Should I use ephemeral or persistent storage

Oso Fallback nodes can use either ephemeral or persistent disk storage. With ephemeral storage, the node downloads the latest rules and data from Oso Cloud on startup; if Oso Cloud is unreachable, the node fails to start.

With persistent storage, the node attempts to start from the existing data in its local directory. As long as the directory contains a valid copy of rules and data (e.g., from a previous run), the node can start even if Oso Cloud is unavailable.

To start new nodes from uninitialized storage when Oso Cloud is down, see [Use Oso Fallback Offline Snapshots](#use-oso-fallback-offline-snapshots).

### Deploy Oso Fallback node

The Oso Fallback node is distributed as a Docker image in [Oso's public repository](https://gallery.ecr.aws/osohq/fallback) with support for `amd64` and `arm64` architectures. It binds to port 8080 in the container, which should be mapped to the desired host port.

**Pull the latest image from the repository**

```bash
docker pull public.ecr.aws/osohq/fallback:latest
```

#### Configure Oso Fallback

Set the following required environment variables to start the Oso Fallback node:

| Variable | Description |
|---------------------|-------------|
| `OSO_API_TOKEN` | Read-only API key for the environment.<br/><br/>Create a new API key [here](/docs/app-integration/oso-cloud-configuration/manage-organization-settings#create-new-api-keys). This should be a different key from the one your application uses to connect to Oso Cloud. |
| `OSO_ENVIRONMENT` | ID of the target environment.<br/><br/>Found on the [Environments page](https://ui.osohq.com/settings/?tab=environments). |
| `OSO_TENANT` | ID of the tenant where the environment resides.<br/><br/>Found at the top of the [Settings page](https://ui.osohq.com/settings). |

Optional variables control behavior such as logging, telemetry, and port configuration:
| Variable | Default | Accepted Values | Description |
|---------------------|---------|-----------------|-------------|
| `OSO_CLIENT_ID` | Random UUID | String containing the following characters: `a-zA-Z0-9.-` | Unique ID for each Fallback node. Used to correlate clients on the [Fallback status page](https://ui.osohq.com/settings/?tab=fallback). If set, must be unique per restart or deployment. |
| `OSO_DISABLE_ERROR_LOGGING` | false | true, false | Disable sending error logs to Oso. |
| `OSO_DISABLE_TELEMETRY` | false | true, false | Disable sending usage data to Oso. |
| `OSO_LOG_LEVEL` | error | info, warn, error | Log verbosity. |
| `OSO_PORT` | 8080 | 0 to 65535 | Port the node listens on. |
| `OSO_DIRECTORY` | ./.oso | String containing a valid path | Path to store rule and data snapshots. |

#### Configure load balancer

Oso Fallback nodes do not respond to [health checks](#health-checks) until rules and data are loaded. On first start or when using ephemeral storage, the node must download data from Oso Cloud. Download time depends on environment size. Configure a health check grace period to allow this initialization before health checks begin.

### Use offline snapshots

Existing Oso Fallback nodes continue running if Oso Cloud becomes unavailable. However, new nodes cannot start unless the data directory is already initialized.

If you want to ensure that new Oso Fallback nodes can always start—even when Oso Cloud is down—use the `--export` flag to create a snapshot from a running node, and the `--import` flag to seed new nodes with that snapshot.

Export a snapshot:

```shell
docker run \
    --env-file=.env \
    --volume "${PWD}:/export" \
    public.ecr.aws/osohq/fallback:latest \
    --export /export/oso-data.snapshot
```

Start a node with the snapshot:

```shell
docker run \
    --env-file=.env \
    -p 127.0.0.1:8080:8080 \
    --volume "${PWD}:/import" \
    public.ecr.aws/osohq/fallback:latest \
    --import /import/oso-data.snapshot
```

The `--volume` flag is required to read and write the snapshot file outside the container. The node will still attempt to fetch the latest data from Oso Cloud, but `--import` allows startup when the data directory is empty and Oso Cloud is unreachable.

### Update Client SDKs

Client SDKs support a `fallbackUrl` parameter for specifying the address of an Oso Fallback node or load balancer. On each request, the SDK attempts to contact Oso Cloud first. If it encounters repeated HTTP 400, HTTP 5xx, or connection errors, it retries the same request against the fallback URL after exhausting all Oso Cloud retry attempts.

The following example shows how to initialize the Oso Client SDK with an Oso Fallback node running on the same host as the application, listening on port 8080:

```javascript
const apiKey = "<your api key>";
const oso = new Oso("https://api.osohq.com", apiKey, { fallbackUrl: "http://localhost:8080" });
```

#### Test Oso Fallback

To verify that Oso Fallback works as expected, perform chaos testing by partitioning a subset of authorization requests away from Oso Cloud. This forces the client SDK to fall back to the local node. You can simulate failure by using a proxy or setting the Oso Cloud URL to an unresolvable hostname in the client SDK configuration.

To confirm fallback usage, inspect the `oso_fallback_http_requests` metric exposed by each Oso Fallback node (see [Monitoring](#monitor-oso-fallback) for details).


## Scale and size Fallback Nodes

The required resources depend on your environment size and query load. The [minimum system requirements](#system-requirements) support starting a node and running test workloads, but are unlikely to meet production needs.

### Scaling the CPU

Each Oso Fallback node runs independently. You can scale capacity by increasing CPU per node or deploying additional nodes. When autoscaling, account for startup time needed to download rules and data. Monitor average CPU usage across all nodes and scale up when utilization exceeds 80%.

### Sizing the memory

Fallback nodes use minimal memory at baseline but rely on in-memory caching to accelerate query evaluation. The required cache size depends on query diversity. Monitor peak memory usage and increase memory per node if utilization exceeds 80%.

### Sizing the disk

Each node stores a local copy of rules and data on disk. When updates are available, the node downloads the new version while continuing to serve from the current one—temporarily doubling disk usage. Monitor disk utilization and scale up if it exceeds 65%. Use low-latency, high-throughput storage to avoid query performance bottlenecks.

## Monitor Oso Fallback

Monitor host-level metrics (CPU, memory, disk) to size Oso Fallback nodes appropriately and determine when to add capacity. Each node also exposes Prometheus metrics for snapshot freshness and usage.

### Health checks

Each node exposes a health check endpoint at `/healthcheck`. It returns `200 OK` only after the node has successfully loaded a valid copy of rules and data, indicating that it is ready to respond to authorization queries.

### Oso Fallback metrics

Available at the `/metrics` endpoint:

- **`oso_fallback_snapshot_age_ms`**
Age of the rules and data currently loaded by the node, in milliseconds. Nodes attempt to fetch updated data every 30 minutes. A higher value indicates staler data.

Example:
  oso_fallback_snapshot_age_ms 624611
  ```

- **`oso_fallback_http_requests`**
Histogram of HTTP response latencies for requests processed by the Fallback node. Useful for tracking usage and latency distribution.

Example buckets:

  ```
  oso_fallback_http_requests_bucket{path="/api/authorize",status_code="200",le="0.005"} 119
  oso_fallback_http_requests_bucket{path="/api/authorize",status_code="200",le="0.01"} 119
... oso_fallback_http_requests_bucket{path="/api/authorize",status_code="200",le="10"} 119
  oso_fallback_http_requests_bucket{path="/api/authorize",status_code="200",le="+Inf"} 119
  ```
