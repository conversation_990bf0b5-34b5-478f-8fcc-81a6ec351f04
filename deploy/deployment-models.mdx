---
title: Deployment models
sidebarTitle: "Deployment models"
---

Oso supports three deployment models: **Cloud**, **Hybrid**, and **Self-Hosted**. Each model offers different trade-offs in infrastructure control, operational complexity, and fault tolerance.

| Feature | Cloud | Hybrid | Self-Hosted |
|---------|-------|--------|-------------|
| **Setup Time** | < 5 minutes | 1-2 hours | 1-2 days |
| **Operational Overhead** | None | Low | Medium-High |
| **Infrastructure Control** | Fully managed by Oso | Cloud + on-prem fallback | Fully customer-managed |
| **Data Residency** | Oso Cloud | Your choice | Your infrastructure |
| **Scalability** | Automatic | Automatic + fallback | Manual scaling |
| **Use Case** | Getting started, most production apps | High availability requirements | Compliance, air-gapped environments |

---

## Cloud Deployment

The **Cloud** model, Oso hosts and manages the authorization service, including infrastructure, scaling, and monitoring.

### When to Choose Cloud

- Getting started with Oso authorization
- Production apps without custom compliance requirements
- Teams that don’t want to manage infra for authorization

### Key Benefits

- **Zero operational overhead**: Oso Cloud handles all authorization requests
- **Global distribution**: Your rules and data are replicated across regions, from Singapore to San Francisco
- **Automatic scaling**: Handles traffic spikes without configuration
- **Built-in monitoring**: Comprehensive observability included
- **Fastest time to value**: Production-ready in minutes

### Steps to Deploy

1. **Create an account** at [ui.osohq.com](https://ui.osohq.com)
2. **Install the SDK** for your preferred language
3. **Define your policy** using Polar syntax
4. **Start making authorization calls** from your application

### Pricing & Billing

Cloud deployment uses usage-based pricing. Billing is handled through the Oso Cloud dashboard with support for enterprise invoicing.

---

## Hybrid Deployment

The **Hybrid** model uses Oso Cloud as the primary service and a local Fallback node for failover.

### When to Choose Hybrid

- High availability requirements that exceed standard cloud SLAs
- Regulatory compliance requiring some on-premises presence
- Disaster recovery scenarios where cloud connectivity might be interrupted
- Organizations wanting cloud convenience with on-premises backup

### How It Works

1. **Primary cloud service** handles all normal authorization requests
2. **Fallback nodes** deployed in your infrastructure sync policy and facts
3. **Automatic failover** when cloud connectivity is lost
4. **Seamless failback** when cloud connectivity is restored

### Key Benefits

- **Maximum uptime**: Authorization continues even during cloud outages
- **Consistent performance**: Local fallback reduces latency during failures
- **Simplified operations**: Most management still handled by Oso Cloud
- **Data sovereignty**: Keep a copy of authorization data on-premises

### Architecture

```
[Your App] → [Oso Cloud] (primary)
     ↓
[Fallback Node] (your infrastructure)
```

### Setup Requirements

- Container orchestration platform (Kubernetes, Docker Swarm, or ECS)
- Network connectivity to Oso Cloud for synchronization
- Storage for local policy and data caching
- Load balancer configuration for failover logic

### Steps to Deploy

1. **Enable hybrid mode** in your Oso Cloud dashboard
2. **Download the fallback node** container image
3. **Configure synchronization** with your cloud environment
4. **Test failover scenarios** in your staging environment
5. **Deploy to production** with monitoring

---

## Self-Hosted Deployment

The **Self-Hosted** model runs the full Oso stack in your infrastructure with no external dependencies.

### When to Choose Self-Hosted

- Strict compliance requirements prohibiting third-party services
- Air-gapped environments without internet connectivity
- Full data sovereignty requirements
- Organizations needing complete infrastructure control

### Current Status

Self-Hosted deployment is currently in **Beta** with support for **AWS-based deployments**. We're actively developing support for other cloud providers and on-premises environments.

**Interested in non-AWS environments?** [Contact us](https://www.osohq.com/meet-eng?utm_source=cloud-docs&utm_content=deployment) to discuss your requirements and join our early access program.

### Compatibility Matrix

| Feature | Cloud | Self-Hosted |
|---------|-------|-------------|
| **Deployment** | Managed by Oso | AWS (others in development) |
| **Monitoring** | Managed by Oso | Datadog, Honeycomb integration |
| **Storage** | Managed by Oso | S3 + MSK in your AWS account |
| **Management Tools** | Web UI + CLI | `oso-manager` CLI tool |
| **Scaling** | Automatic | Horizontal scaling on AWS ECS |
| **Updates** | Automatic | Manual with provided tooling |

## Next Steps

### Ready to Get Started?

- **Try Oso Cloud:** Create a free account at [ui.osohq.com](https://ui.osohq.com)
- **Explore Hybrid:** [Contact us](https://www.osohq.com/meet-eng?utm_source=cloud-docs&utm_content=deployment) to discuss Fallback node setup
- **Evaluate Self-Hosted:** [Schedule a technical consultation](https://www.osohq.com/meet-eng?utm_source=cloud-docs&utm_content=deployment)
