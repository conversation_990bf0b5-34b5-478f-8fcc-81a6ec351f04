---
inclusion: always
---

# Oso Cloud Documentation Guidelines

## Core Product Concepts

**Oso Cloud** is a centralized authorization-as-a-service platform with these key components:

- **Polar**: Domain-specific language for authorization logic (not a general programming language)
- **Facts**: Authorization data (users, roles, relationships) - always use this term, not "data" or "records"
- **Policies**: Authorization rules written in Polar - these define "who can do what"
- **Local Authorization**: Hybrid approach executing authorization against local databases
- **Centralized Authorization**: Traditional API-based authorization queries

## Terminology Standards

Use these exact terms consistently:
- "authorization" not "auth" in formal documentation
- "Polar" not "polar" when referring to the language
- "facts" not "authorization data" or "permission data"
- "Oso Cloud" not "OSO" or "oso"

## Documentation Writing Principles

### Priority: Speed to Value
- Assume readers want to solve a specific problem quickly
- Start with the simplest possible implementation

### Content Structure
- **Most important information first** - no lengthy introductions
- **Short paragraphs** (3-4 lines maximum)
- **Scannable sections** with clear subheadings
- **Progressive disclosure** using `<details>` tags for optional content
- **Visual breaks** with code samples, bullet points, numbered lists, and bolding on key terms
- **Active voice** - write direct, actionable sentences
- **Be concise and clear** - Use simple, direct language. Focus on one idea per sentence. Make every word count.
- **Avoid AI cliches** - Avoid common markers of AI generated content, like breaking up sentences with a "-".
- **Apply consistent punctuation** - Every sentence should have a period. Incomplete sentences in list format SHOULD omit a period.

### Writing Voice
Use active voice to create clear, direct instructions:

**Good (Active Voice):**
- "Configure your API key in the dashboard"
- "Oso Cloud evaluates policies against your facts"
- "Add the user to the organization with this API call"
- "The policy grants access when users belong to the same team"

**Avoid (Passive Voice):**
- "Your API key should be configured in the dashboard"
- "Policies are evaluated against your facts by Oso Cloud"
- "The user can be added to the organization with this API call"
- "Access is granted by the policy when users belong to the same team"

### Code Examples
- Always provide complete, runnable examples
- Annotate complex code with inline comments
- Show both success and error cases
- Use realistic data in examples (avoid "foo", "bar")

### Authorization Patterns
When documenting authorization patterns:
- Start with the business use case
- Show the Polar policy implementation
- Demonstrate the API calls
- Include troubleshooting tips

### Target Audience
Primary: Developers implementing authorization in applications
Secondary: Technical decision-makers evaluating authorization solutions

Write for developers who need sophisticated authorization beyond basic RBAC but may be new to Oso Cloud and authorization-as-a-service concepts.