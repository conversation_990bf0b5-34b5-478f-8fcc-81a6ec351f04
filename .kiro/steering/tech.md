# Technology Stack

## Documentation Platform
- **Mintlify**: Documentation platform for building interactive docs
- **MDX**: Markdown with JSX components for rich content
- **OpenAPI**: API specification and interactive playground integration

## Build System & Commands

### Development
```bash
# Install Mintlify CLI globally
npm i -g mintlify

# Start local development server
mint dev

# Reinstall dependencies if dev server fails
mint install
```

### Deployment
- Auto-deployment via GitHub App integration
- Changes deploy to production on push to default branch
- Manual deployment through Mintlify dashboard

## Configuration Files
- `docs.json`: Main Mintlify configuration (navigation, theme, API settings)
- `openapi.yaml`: API specification for interactive documentation
- `_nav-example.js`: Navigation configuration examples and patterns
- `_page-example.mdx`: Page configuration examples and frontmatter options

## Content Structure
- **MDX files**: Documentation pages with frontmatter metadata
- **JSON configuration**: Navigation structure and site settings
- **OpenAPI integration**: Automatic API reference generation
- **Asset management**: Images, logos, and static files

## Key Features
- Interactive API playground
- Multi-language code examples (curl, Python, JavaScript)
- Contextual AI integration (<PERSON><PERSON><PERSON><PERSON>, <PERSON>)
- Search functionality