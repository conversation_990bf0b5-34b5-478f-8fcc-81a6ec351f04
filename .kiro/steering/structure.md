# Project Structure

## Root Level
- `docs.json`: Main Mintlify configuration file
- `openapi.yaml`: API specification for reference docs
- `index.mdx`: Homepage/landing page
- `README.md`: Project setup and development instructions
- `_nav-example.js`: Navigation configuration examples
- `_page-example.mdx`: Page frontmatter examples

## Content Organization

### `/get-started/`
- Introduction and quickstart guides
- Entry point for new users

### `/develop/`
Main development documentation organized by workflow:
- `/local-dev/`: Environment setup and development tools
- `/policies/`: Writing authorization policies in Polar
  - `/patterns/`: Common authorization patterns and examples
- `/facts/`: Managing authorization data
- `/enforce/`: Implementing authorization checks
- `/troubleshooting/`: Debugging and performance guides

### `/deploy/`
Production deployment and operations:
- Deployment models and infrastructure
- Account management and SSO
- Security, backups, and migrations

### `/learn/`
Educational content:
- `/tutorials/`: Step-by-step walkthroughs
- `/guides/`: Topic-specific how-to guides

### `/reference/`
Technical reference documentation:
- `/api/`: Auto-generated API documentation from OpenAPI
- `/polar/`: Polar language reference
- `/sdks/`: Client library documentation

## Asset Directories
- `/images/`: Screenshots and diagrams
- `/logo/`: Brand assets (light/dark variants)
- `/snippets/`: Reusable code snippets

## Navigation Structure
Uses Mintlify's tab-based navigation:
- **Docs**: Main documentation (Get Started → Develop → Deploy)
- **Reference**: Technical specs (Polar, API, SDKs)  
- **Learn**: Tutorials and guides

## File Naming Conventions
- Use kebab-case for file and folder names
- MDX files for all content pages
- Organize by user journey and logical grouping
- Keep folder depth reasonable (max 3 levels)

