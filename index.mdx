---
title: Oso Cloud Documentation
---



Oso Cloud is a centralized authorization service built on Polar, our logic programming language designed for expressing permission systems,
and SQLite, the world's most battle-tested database engine.

Polar can express any model, including [RBAC](/core-concepts/policies/rbac), [ReBAC](/core-concepts/policies/rebac), [ABAC](/core-concepts/policies/abac), [fine-grained authorization](/core-concepts/policies/fga), [organizational hierarchies](/core-concepts/policies/organizational-hierarchies), and more.

When you need to answer an authorization question, you call our APIs via HTTP or Client SDK. Oso Cloud evaluates the request against your authorization logic and data, returning a boolean, list, or logic to execute against your database.

## Getting Started

<CardGroup cols={2}>
  <Card
    title="Quickstart"
    icon="pen-to-square"
    href="https://mintlify.com/docs/quickstart"
  >
    Get started with Oso Cloud in minutes
  </Card>
  <Card
    title="Writing Policies"
    icon="pen-to-square"
    href="https://mintlify.com/docs/quickstart"
  >
    Model your authorization policies in Polar, our DSL for permissions logic
  </Card>
  <Card
    title="Storing Facts"
    icon="image"
    href="https://mintlify.com/docs/development"
  >
    Store and sync your authorization data with Oso Cloud
  </Card>
    <Card
    title="Local Authorization"
    icon="image"
    href="https://mintlify.com/docs/development"
  >
    Use Oso Cloud to evaluate authorization decisions against your local database
  </Card>
</CardGroup>

