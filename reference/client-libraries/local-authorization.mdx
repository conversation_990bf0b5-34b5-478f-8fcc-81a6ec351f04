---
title: Local Authorization
sidebarTitle: "Local authorization"
---

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies. This approach provides significant performance benefits for applications that need to display large lists of authorized resources.

Instead of fetching all resources and checking authorization for each one, local authorization generates SQL that your database can execute directly. This approach:

- **Reduces Network Traffic**: Only authorized resources are returned from the database
- **Improves Performance**: Database-level filtering is much faster than application-level filtering
- **Scales Better**: Performance doesn't degrade as your dataset grows
- **Maintains Security**: Authorization logic stays centralized in your policies

Local authorization works by translating your Oso Cloud policies into SQL WHERE clauses that can be integrated with your existing database queries.

### When to Use Local Authorization

**Use local authorization for:**
- Displaying lists of resources (dashboards, search results, etc.)
- Pagination with large datasets
- Reports and analytics
- Any scenario where you need to filter many resources

**Use regular authorization for:**
- Single resource access checks
- Actions that modify resources
- Complex authorization logic that doesn't translate well to SQL
- When you need the most up-to-date authorization decision

## List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

<Tabs>
<Tab title="Node.js">

```javascript
oso.listLocal(actor: Value, action: string, resourceType: string, column: string): Promise<string>
```

**Basic Example:**
```javascript
import { Value } from 'oso-cloud';

const alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
const sqlFilter = await oso.listLocal(alice, "read", "Repository", "id");
console.log("SQL Filter:", sqlFilter);
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Kysely):**
```javascript
import { Kysely } from 'kysely';

async function getReadableRepositories(userId: string) {
  const user = new Value("User", userId);
  const filter = await oso.listLocal(user, "read", "Repository", "id");
  
  // Use the filter in your database query
  const repositories = await db
    .selectFrom('repositories')
    .selectAll()
    .where(sql`${sql.raw(filter)}`)
    .execute();
    
  return repositories;
}
```

**Advanced Usage:**
```javascript
// Get repositories Alice can admin with additional filtering
const adminFilter = await oso.listLocal(alice, "admin", "Repository", "id");
const repositories = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(adminFilter)}`)
  .where('active', '=', true)
  .where('created_at', '>', new Date('2023-01-01'))
  .execute();
```

</Tab>
<Tab title="Python">

```python
oso.list_local(actor: Value, action: str, resource_type: str, column: str) -> str
```

**Basic Example:**
```python
from oso_cloud import Value

alice = Value("User", "alice")

# Get SQL filter for repositories Alice can read
sql_filter = oso.list_local(alice, "read", "Repository", "id")
print("SQL Filter:", sql_filter)
# Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (SQLAlchemy):**
```python
from sqlalchemy import text
from sqlalchemy.orm import Session

def get_readable_repositories(session: Session, user_id: str):
    user = Value("User", user_id)
    filter_sql = oso.list_local(user, "read", "Repository", "id")
    
    # Use the filter in your database query
    query = text(f"""
        SELECT * FROM repositories 
        WHERE {filter_sql}
        ORDER BY created_at DESC
    """)
    
    repositories = session.execute(query).fetchall()
    return repositories
```

**Advanced Usage with ORM:**
```python
from sqlalchemy import text

def get_user_repositories(session: Session, user_id: str):
    user = Value("User", user_id)
    
    # Get different permission levels
    read_filter = oso.list_local(user, "read", "Repository", "id")
    admin_filter = oso.list_local(user, "admin", "Repository", "id")
    
    # Combine with ORM query
    repositories = session.query(Repository).filter(
        text(read_filter)
    ).filter(
        Repository.active == True
    ).all()
    
    # Add permission level to each repository
    admin_repo_ids = session.execute(text(f"SELECT id FROM repositories WHERE {admin_filter}")).fetchall()
    admin_ids = {row[0] for row in admin_repo_ids}
    
    for repo in repositories:
        repo.user_can_admin = repo.id in admin_ids
    
    return repositories
```

</Tab>
<Tab title="Go">

```go
osoClient.ListLocal(actor Value, action string, resourceType string, column string) (string, error)
```

**Basic Example:**
```go
import oso "github.com/osohq/go-oso-cloud/v2"

alice := oso.NewValue("User", "alice")

// Get SQL filter for repositories Alice can read
sqlFilter, err := osoClient.ListLocal(alice, "read", "Repository", "id")
if err != nil {
    log.Fatal(err)
}
fmt.Printf("SQL Filter: %s\n", sqlFilter)
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (GORM):**
```go
import (
    "gorm.io/gorm"
    oso "github.com/osohq/go-oso-cloud/v2"
)

type Repository struct {
    ID        string `gorm:"primaryKey"`
    Name      string
    Active    bool
    CreatedAt time.Time
}

func GetReadableRepositories(db *gorm.DB, userID string) ([]Repository, error) {
    user := oso.NewValue("User", userID)
    filter, err := osoClient.ListLocal(user, "read", "Repository", "id")
    if err != nil {
        return nil, err
    }
    
    var repositories []Repository
    err = db.Where(filter).Where("active = ?", true).Find(&repositories).Error
    return repositories, err
}
```

**Advanced Usage:**
```go
func GetUserRepositoriesWithPermissions(db *gorm.DB, userID string) ([]RepositoryWithPermissions, error) {
    user := oso.NewValue("User", userID)
    
    // Get different permission filters
    readFilter, err := osoClient.ListLocal(user, "read", "Repository", "id")
    if err != nil {
        return nil, err
    }
    
    adminFilter, err := osoClient.ListLocal(user, "admin", "Repository", "id")
    if err != nil {
        return nil, err
    }
    
    // Get readable repositories
    var repositories []Repository
    err = db.Where(readFilter).Find(&repositories).Error
    if err != nil {
        return nil, err
    }
    
    // Get admin repository IDs
    var adminRepos []Repository
    err = db.Select("id").Where(adminFilter).Find(&adminRepos).Error
    if err != nil {
        return nil, err
    }
    
    adminSet := make(map[string]bool)
    for _, repo := range adminRepos {
        adminSet[repo.ID] = true
    }
    
    // Combine results
    result := make([]RepositoryWithPermissions, len(repositories))
    for i, repo := range repositories {
        result[i] = RepositoryWithPermissions{
            Repository: repo,
            CanAdmin:   adminSet[repo.ID],
        }
    }
    
    return result, nil
}
```

</Tab>
<Tab title="Java">

```java
oso.listLocal(actor: Value, action: String, resourceType: String, column: String): String throws ApiException
```

**Basic Example:**
```java
import com.osohq.oso_cloud.*;

Value alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
String sqlFilter = oso.listLocal(alice, "read", "Repository", "id");
System.out.println("SQL Filter: " + sqlFilter);
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (JPA):**
```java
import javax.persistence.EntityManager;
import javax.persistence.Query;

@Entity
public class Repository {
    @Id
    private String id;
    private String name;
    private boolean active;
    private LocalDateTime createdAt;
    // getters and setters...
}

@Service
public class RepositoryService {
    
    @Autowired
    private EntityManager entityManager;
    
    public List<Repository> getReadableRepositories(String userId) throws ApiException {
        Value user = new Value("User", userId);
        String filter = oso.listLocal(user, "read", "Repository", "id");
        
        String jpql = "SELECT r FROM Repository r WHERE " + filter + " AND r.active = true";
        Query query = entityManager.createQuery(jpql, Repository.class);
        
        return query.getResultList();
    }
}
```

**Advanced Usage with Native Queries:**
```java
public class RepositoryWithPermissions {
    private Repository repository;
    private boolean canAdmin;
    // constructors, getters, setters...
}

public List<RepositoryWithPermissions> getUserRepositoriesWithPermissions(String userId) throws ApiException {
    Value user = new Value("User", userId);
    
    String readFilter = oso.listLocal(user, "read", "Repository", "r.id");
    String adminFilter = oso.listLocal(user, "admin", "Repository", "r.id");
    
    String sql = """
        SELECT r.*, 
               CASE WHEN admin_repos.id IS NOT NULL THEN true ELSE false END as can_admin
        FROM repositories r
        LEFT JOIN (SELECT id FROM repositories WHERE %s) admin_repos ON r.id = admin_repos.id
        WHERE %s AND r.active = true
        ORDER BY r.created_at DESC
        """.formatted(adminFilter, readFilter);
    
    Query query = entityManager.createNativeQuery(sql);
    List<Object[]> results = query.getResultList();
    
    return results.stream()
        .map(row -> new RepositoryWithPermissions(
            mapToRepository(row), 
            (Boolean) row[row.length - 1]
        ))
        .collect(Collectors.toList());
}
```

</Tab>
<Tab title="Ruby">

```ruby
oso.list_local(actor, action, resource_type, column)
```

**Basic Example:**
```ruby
alice = { type: "User", id: "alice" }

# Get SQL filter for repositories Alice can read
sql_filter = oso.list_local(alice, "read", "Repository", "id")
puts "SQL Filter: #{sql_filter}"
# Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Active Record):**
```ruby
class Repository < ApplicationRecord
  def self.readable_by(user_id)
    user = { type: "User", id: user_id }
    filter = $oso.list_local(user, "read", "Repository", "id")
    
    where(filter).where(active: true)
  end
  
  def self.administrable_by(user_id)
    user = { type: "User", id: user_id }
    filter = $oso.list_local(user, "admin", "Repository", "id")
    
    where(filter)
  end
end

# Usage in controller
class RepositoriesController < ApplicationController
  def index
    @repositories = Repository.readable_by(current_user.id)
                             .includes(:organization)
                             .order(created_at: :desc)
                             .page(params[:page])
  end
end
```

**Advanced Usage:**
```ruby
class Repository < ApplicationRecord
  def self.with_permissions_for(user_id)
    user = { type: "User", id: user_id }
    
    read_filter = $oso.list_local(user, "read", "Repository", "repositories.id")
    admin_filter = $oso.list_local(user, "admin", "Repository", "repositories.id")
    
    select("repositories.*, 
            CASE WHEN admin_repos.id IS NOT NULL THEN true ELSE false END as can_admin")
      .joins("LEFT JOIN (SELECT id FROM repositories WHERE #{admin_filter}) admin_repos ON repositories.id = admin_repos.id")
      .where(read_filter)
      .where(active: true)
  end
end

# Usage
repositories = Repository.with_permissions_for(current_user.id)
repositories.each do |repo|
  puts "#{repo.name} - Can admin: #{repo.can_admin}"
end
```

</Tab>
<Tab title=".NET">

```csharp
oso.ListLocal(actor: Value, action: string, resourceType: string, column: string): Task<LocalAuthResult>
```

**Basic Example:**
```csharp
using OsoCloud;

var alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
var result = await oso.ListLocal(alice, "read", "Repository", "Id");
Console.WriteLine($"SQL Filter: {result.Sql}");
// Output: "Id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Entity Framework):**
```csharp
using Microsoft.EntityFrameworkCore;

public class Repository
{
    public string Id { get; set; }
    public string Name { get; set; }
    public bool Active { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class RepositoryService
{
    private readonly ApplicationDbContext _context;
    private readonly OsoCloudClient _oso;

    public async Task<List<Repository>> GetReadableRepositoriesAsync(string userId)
    {
        var user = new Value("User", userId);
        var filterResult = await _oso.ListLocal(user, "read", "Repository", "Id");
        
        var repositories = await _context.Repositories
            .FromSqlRaw($"SELECT * FROM Repositories WHERE {filterResult.Sql} AND Active = 1")
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync();
            
        return repositories;
    }
}
```

**Advanced Usage with LINQ:**
```csharp
public class RepositoryWithPermissions
{
    public Repository Repository { get; set; }
    public bool CanAdmin { get; set; }
}

public async Task<List<RepositoryWithPermissions>> GetUserRepositoriesWithPermissionsAsync(string userId)
{
    var user = new Value("User", userId);
    
    var readResult = await _oso.ListLocal(user, "read", "Repository", "Id");
    var adminResult = await _oso.ListLocal(user, "admin", "Repository", "Id");
    
    // Get readable repositories
    var readableRepos = await _context.Repositories
        .FromSqlRaw($"SELECT * FROM Repositories WHERE {readResult.Sql} AND Active = 1")
        .ToListAsync();
    
    // Get admin repository IDs
    var adminRepoIds = await _context.Repositories
        .FromSqlRaw($"SELECT Id FROM Repositories WHERE {adminResult.Sql}")
        .Select(r => r.Id)
        .ToListAsync();
    
    var adminSet = new HashSet<string>(adminRepoIds);
    
    return readableRepos.Select(repo => new RepositoryWithPermissions
    {
        Repository = repo,
        CanAdmin = adminSet.Contains(repo.Id)
    }).ToList();
}
```

</Tab>
</Tabs>

## Authorize Local

Generates a SQL query to determine if an actor can perform an action on a specific resource.

<Tabs>
<Tab title="Node.js">

```javascript
oso.authorizeLocal(actor: Value, action: string, resource: Value): Promise<string>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get SQL query to check if Alice can read the repository
const authQuery = await oso.authorizeLocal(alice, "read", repo);
console.log("Authorization Query:", authQuery);

// Use in database query
const result = await db.raw(authQuery);
const isAuthorized = result.rows[0]?.allowed || false;
```

</Tab>
<Tab title="Python">

```python
oso.authorize_local(actor: Value, action: str, resource: Value) -> str
```

**Example:**
```python
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Get SQL query to check if Alice can read the repository
auth_query = oso.authorize_local(alice, "read", repo)
print("Authorization Query:", auth_query)

# Use in database query
result = session.execute(text(auth_query)).fetchone()
is_authorized = result[0] if result else False
```

</Tab>
<Tab title="Go">

```go
osoClient.AuthorizeLocal(actor Value, action string, resource Value) (AuthorizeLocalResult, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Get SQL query to check if Alice can read the repository
result, err := osoClient.AuthorizeLocal(alice, "read", repo)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Authorization Query: %s\n", result.Sql)

// Use in database query (example with database/sql)
var allowed bool
err = db.QueryRow(result.Sql).Scan(&allowed)
if err != nil {
    log.Fatal(err)
}
```

</Tab>
<Tab title="Java">

```java
oso.authorizeLocal(actor: Value, action: String, resource: Value): String throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Get SQL query to check if Alice can read the repository
String authQuery = oso.authorizeLocal(alice, "read", repo);
System.out.println("Authorization Query: " + authQuery);

// Use in database query
Query query = entityManager.createNativeQuery(authQuery);
Boolean isAuthorized = (Boolean) query.getSingleResult();
```

</Tab>
<Tab title="Ruby">

```ruby
oso.authorize_local(actor, action, resource)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Get SQL query to check if Alice can read the repository
auth_query = oso.authorize_local(alice, "read", repo)
puts "Authorization Query: #{auth_query}"

# Use in database query
result = ActiveRecord::Base.connection.execute(auth_query)
is_authorized = result.first&.dig("allowed") || false
```

</Tab>
<Tab title=".NET">

```csharp
oso.AuthorizeLocal(actor: Value, action: string, resource: Value): Task<LocalAuthResult>
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get SQL query to check if Alice can read the repository
var result = await oso.AuthorizeLocal(alice, "read", repo);
Console.WriteLine($"Authorization Query: {result.Sql}");

// Use in database query
var isAuthorized = await _context.Database
    .SqlQueryRaw<bool>(result.Sql)
    .FirstOrDefaultAsync();
```

</Tab>
</Tabs>

## Actions Local

Generates a SQL query to get all actions an actor can perform on a specific resource.

<Tabs>
<Tab title="Node.js">

```javascript
oso.actionsLocal(actor: Value, resource: Value): Promise<string>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get SQL query for actions Alice can perform on the repository
const actionsQuery = await oso.actionsLocal(alice, repo);
console.log("Actions Query:", actionsQuery);

// Use in database query
const result = await db.raw(actionsQuery);
const allowedActions = result.rows.map(row => row.action);
console.log("Allowed actions:", allowedActions);
```

</Tab>
<Tab title="Python">

```python
oso.actions_local(actor: Value, resource: Value) -> str
```

**Example:**
```python
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Get SQL query for actions Alice can perform on the repository
actions_query = oso.actions_local(alice, repo)
print("Actions Query:", actions_query)

# Use in database query
result = session.execute(text(actions_query)).fetchall()
allowed_actions = [row[0] for row in result]
print("Allowed actions:", allowed_actions)
```

</Tab>
<Tab title="Go">

```go
osoClient.ActionsLocal(actor Value, resource Value) (ActionsLocalResult, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Get SQL query for actions Alice can perform on the repository
result, err := osoClient.ActionsLocal(alice, repo)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Actions Query: %s\n", result.Sql)

// Use in database query
rows, err := db.Query(result.Sql)
if err != nil {
    log.Fatal(err)
}
defer rows.Close()

var allowedActions []string
for rows.Next() {
    var action string
    err := rows.Scan(&action)
    if err != nil {
        log.Fatal(err)
    }
    allowedActions = append(allowedActions, action)
}
```

</Tab>
<Tab title="Java">

```java
oso.actionsLocal(actor: Value, resource: Value): String throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Get SQL query for actions Alice can perform on the repository
String actionsQuery = oso.actionsLocal(alice, repo);
System.out.println("Actions Query: " + actionsQuery);

// Use in database query
Query query = entityManager.createNativeQuery(actionsQuery);
List<String> allowedActions = query.getResultList();
System.out.println("Allowed actions: " + allowedActions);
```

</Tab>
<Tab title="Ruby">

```ruby
oso.actions_local(actor, resource)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Get SQL query for actions Alice can perform on the repository
actions_query = oso.actions_local(alice, repo)
puts "Actions Query: #{actions_query}"

# Use in database query
result = ActiveRecord::Base.connection.execute(actions_query)
allowed_actions = result.map { |row| row["action"] }
puts "Allowed actions: #{allowed_actions}"
```

</Tab>
<Tab title=".NET">

```csharp
oso.ActionsLocal(actor: Value, resource: Value): Task<LocalAuthResult>
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get SQL query for actions Alice can perform on the repository
var result = await oso.ActionsLocal(alice, repo);
Console.WriteLine($"Actions Query: {result.Sql}");

// Use in database query
var allowedActions = await _context.Database
    .SqlQueryRaw<string>(result.Sql)
    .ToListAsync();
Console.WriteLine($"Allowed actions: {string.Join(", ", allowedActions)}");
```

</Tab>
</Tabs>

## Performance Considerations

### Database Optimization

1. **Indexing**: Ensure proper indexes on columns used in authorization filters
2. **Query Planning**: Review query execution plans for generated SQL
3. **Connection Pooling**: Use connection pooling for database access
4. **Caching**: Cache authorization filters for frequently-accessed resources

### Best Practices

```javascript
// Good: Combine authorization filter with other filters
const repositories = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(authFilter)}`)
  .where('active', '=', true)
  .where('organization_id', '=', orgId)
  .limit(50)
  .execute();

// Avoid: Fetching all authorized resources without additional filtering
const allRepos = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(authFilter)}`)
  .execute(); // Could return thousands of records
```

### Monitoring

Monitor the performance of generated SQL queries:

```javascript
// Log slow queries for optimization
const startTime = Date.now();
const filter = await oso.listLocal(user, action, resourceType, column);
const repositories = await db.query(/* your query with filter */);
const duration = Date.now() - startTime;

if (duration > 1000) {
  console.warn(`Slow local authorization query: ${duration}ms`, {
    user: user.id,
    action,
    resourceType,
    resultCount: repositories.length
  });
}
```

## HTTP API Reference

These client library methods correspond to the following HTTP API endpoints:

- [List Local](/reference/api/local-check-api/post-list_query): `POST /list_local`
- [Authorize Local](/reference/api/local-check-api/post-authorize_query): `POST /authorize_local`
- [Actions Local](/reference/api/local-check-api/post-actions_query): `POST /actions_local`
