---
title: Performance Optimization
description: "Caching, batching, and async patterns for high-performance Node.js applications."
sidebarTitle: "Performance"
---

## Caching Results

```javascript
class CachedAuthorizationService {
  constructor(oso, cacheTimeout = 5 * 60 * 1000) { // 5 minutes
    this.oso = oso;
    this.cache = new Map();
    this.cacheTimeout = cacheTimeout;
  }

  async authorize(user, action, resource) {
    const key = `${user.id}:${action}:${resource.id}`;
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.result;
    }
    
    const result = await this.oso.authorize(user, action, resource);
    this.cache.set(key, { result, timestamp: Date.now() });
    
    return result;
  }
}
```

## Batch Operations

```javascript
// Instead of multiple individual checks
const canRead = await oso.authorize(alice, "read", repo1);
const canWrite = await oso.authorize(alice, "write", repo1);
const canAdmin = await oso.authorize(alice, "admin", repo1);

// Use actions() for multiple permission checks on same resource
const allowedActions = await oso.actions(alice, repo1);
const canRead = allowedActions.includes("read");
const canWrite = allowedActions.includes("write");
const canAdmin = allowedActions.includes("admin");
```

## Parallel Processing

```javascript
// Check multiple resources in parallel
const repositories = [repo1, repo2, repo3];
const authResults = await Promise.all(
  repositories.map(repo => oso.authorize(alice, "read", repo))
);

const accessibleRepos = repositories.filter((repo, index) => authResults[index]);
```

## Next Steps

- **[Testing strategies](/reference/client-libraries/nodejs/testing)** - Test performance optimizations
- **[Local authorization](/reference/client-libraries/nodejs/local-authorization)** - Database-level filtering
