---
title: Testing
description: "Unit testing and integration testing patterns for Node.js applications."
sidebarTitle: "Testing"
---

## Unit Testing with Jest

```javascript
// test/setup.js
import { jest } from '@jest/globals';

// Mock Oso client for testing
jest.mock('oso-cloud', () => ({
  Oso: jest.fn().mockImplementation(() => ({
    authorize: jest.fn().mockResolvedValue(true),
    list: jest.fn().mockResolvedValue([]),
    actions: jest.fn().mockResolvedValue([]),
    insert: jest.fn().mockResolvedValue(undefined),
    delete: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue([]),
    batch: jest.fn().mockResolvedValue(undefined),
    policy: jest.fn().mockResolvedValue(undefined),
    getPolicy: jest.fn().mockResolvedValue(""),
    stats: jest.fn().mockResolvedValue({ num_rules: 0, num_types: 0, num_resources: 0 })
  })),
  Value: jest.fn().mockImplementation((type, id) => ({ type, id }))
}));
```

## Integration Testing

```javascript
// test/authorization.test.js
import { Oso, Value } from 'oso-cloud';

describe('Authorization Service', () => {
  let oso;
  
  beforeEach(() => {
    oso = new Oso({
      url: process.env.OSO_TEST_URL,
      apiKey: process.env.OSO_TEST_API_KEY
    });
  });

  test('should authorize user access', async () => {
    const user = new Value("User", "alice");
    const resource = new Value("Repository", "test-repo");
    
    const allowed = await oso.authorize(user, "read", resource);
    expect(allowed).toBe(true);
  });
});
```

## Test Helpers

```javascript
// test/helpers.js
export function createTestUser(id = "test-user") {
  return new Value("User", id);
}

export function createTestRepository(id = "test-repo") {
  return new Value("Repository", id);
}

export function mockOsoClient() {
  return {
    authorize: jest.fn().mockResolvedValue(true),
    list: jest.fn().mockResolvedValue([]),
    actions: jest.fn().mockResolvedValue([])
  };
}
```

## Next Steps

- **[Authorization checks](/reference/client-libraries/nodejs/authorization-checks)** - Learn the core authorization methods
- **[Facts management](/reference/client-libraries/nodejs/facts-management)** - Test fact operations
