---
title: Authorization Checks
description: "Core authorization methods for checking permissions, listing resources, and querying access patterns."
sidebarTitle: "Authorization Checks"
---

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

## Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```javascript
oso.authorize(actor: Value, action: string, resource: Value, contextFacts?: Fact[]): Promise<boolean>
```

**Basic Example:**
```javascript
import { Value } from 'oso-cloud';

const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
const canRead = await oso.authorize(alice, "read", repo);
console.log(`Alice can read repo: ${canRead}`);

// Check if Alice can write to the repository
const canWrite = await oso.authorize(alice, "write", repo);
console.log(`Alice can write to repo: ${canWrite}`);
```

**With Context Facts:**
```javascript
// Check authorization with temporary context
const canReadPublic = await oso.authorize(
  alice, 
  "read", 
  repo, 
  [["is_public", repo]]
);
```

### Real-World Examples

**Express.js Route Protection:**
```javascript
app.get('/repositories/:id', async (req, res) => {
  try {
    const user = new Value("User", req.user.id);
    const repository = new Value("Repository", req.params.id);
    
    const canRead = await oso.authorize(user, "read", repository);
    if (!canRead) {
      return res.status(403).json({ error: "Access denied" });
    }
    
    const repo = await getRepository(req.params.id);
    res.json(repo);
  } catch (error) {
    res.status(500).json({ error: "Authorization check failed" });
  }
});
```

**Next.js API Route:**
```javascript
// pages/api/repositories/[id]/edit.js
export default async function handler(req, res) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const user = new Value("User", req.user.id);
  const repository = new Value("Repository", req.query.id);
  
  const canWrite = await oso.authorize(user, "write", repository);
  if (!canWrite) {
    return res.status(403).json({ error: "You don't have permission to edit this repository" });
  }
  
  // Handle the edit request
  await updateRepository(req.query.id, req.body);
  res.json({ success: true });
}
```

**Service Layer Authorization:**
```javascript
class DocumentService {
  constructor(oso) {
    this.oso = oso;
  }

  async getDocument(userId, documentId) {
    const user = new Value("User", userId);
    const document = new Value("Document", documentId);
    
    const canRead = await this.oso.authorize(user, "read", document);
    if (!canRead) {
      throw new Error("Access denied to document");
    }
    
    return await this.documentRepository.findById(documentId);
  }

  async shareDocument(userId, documentId, targetUserId) {
    const user = new Value("User", userId);
    const document = new Value("Document", documentId);
    
    // Check if user can share the document
    const canShare = await this.oso.authorize(user, "share", document);
    if (!canShare) {
      throw new Error("You cannot share this document");
    }
    
    // Add sharing permission
    await this.oso.insert(["can_read", new Value("User", targetUserId), document]);
  }
}
```

## List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```javascript
oso.list(actor: Value, action: string, resourceType: string, contextFacts?: Fact[]): Promise<string[]>
```

**Example:**
```javascript
const alice = new Value("User", "alice");

// Get all repositories Alice can read
const readableRepos = await oso.list(alice, "read", "Repository");
console.log("Readable repositories:", readableRepos);

// Get all organizations Alice can admin
const adminOrgs = await oso.list(alice, "admin", "Organization");
console.log("Admin organizations:", adminOrgs);
```

**With Context Facts:**
```javascript
// List with additional context
const publicRepos = await oso.list(
  alice, 
  "read", 
  "Repository",
  [["user_preference", alice, "show_public", true]]
);
```

### Dashboard Implementation

```javascript
class DashboardService {
  constructor(oso) {
    this.oso = oso;
  }

  async getUserDashboard(userId) {
    const user = new Value("User", userId);
    
    // Get all resources the user can access
    const [readableRepos, writableRepos, adminOrgs] = await Promise.all([
      this.oso.list(user, "read", "Repository"),
      this.oso.list(user, "write", "Repository"),
      this.oso.list(user, "admin", "Organization")
    ]);
    
    // Fetch actual data
    const [repositories, organizations] = await Promise.all([
      this.repositoryService.getByIds(readableRepos),
      this.organizationService.getByIds(adminOrgs)
    ]);
    
    return {
      readableRepositories: repositories,
      writableRepositoryIds: writableRepos,
      adminOrganizations: organizations
    };
  }
}
```

**React Hook:**
```javascript
import { useState, useEffect } from 'react';

function useUserResources(userId, action, resourceType) {
  const [resources, setResources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchResources() {
      try {
        setLoading(true);
        const user = new Value("User", userId);
        const resourceIds = await oso.list(user, action, resourceType);
        
        // Fetch full resource data
        const resourceData = await fetchResourcesByIds(resourceIds);
        setResources(resourceData);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    if (userId) {
      fetchResources();
    }
  }, [userId, action, resourceType]);

  return { resources, loading, error };
}

// Usage in component
function UserDashboard({ userId }) {
  const { resources: repositories, loading } = useUserResources(userId, "read", "Repository");
  
  if (loading) return <div>Loading...</div>;
  
  return (
    <div>
      <h2>Your Repositories</h2>
      {repositories.map(repo => (
        <div key={repo.id}>{repo.name}</div>
      ))}
    </div>
  );
}
```

## List Actions

Fetches all actions that an actor can perform on a specific resource.

```javascript
oso.actions(actor: Value, resource: Value, contextFacts?: Fact[]): Promise<string[]>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
const allowedActions = await oso.actions(alice, repo);
console.log("Allowed actions:", allowedActions);
// Output: ["read", "write"] or ["read", "write", "admin"]
```

### UI Permission Controls

```javascript
// React component with conditional rendering
function RepositoryCard({ repository, userId }) {
  const [allowedActions, setAllowedActions] = useState([]);

  useEffect(() => {
    async function fetchActions() {
      const user = new Value("User", userId);
      const repo = new Value("Repository", repository.id);
      const actions = await oso.actions(user, repo);
      setAllowedActions(actions);
    }
    
    fetchActions();
  }, [repository.id, userId]);

  return (
    <div className="repository-card">
      <h3>{repository.name}</h3>
      <p>{repository.description}</p>
      
      <div className="actions">
        {allowedActions.includes("write") && (
          <button onClick={() => editRepository(repository.id)}>Edit</button>
        )}
        
        {allowedActions.includes("admin") && (
          <button onClick={() => managePermissions(repository.id)}>Permissions</button>
        )}
        
        {allowedActions.includes("delete") && (
          <button 
            onClick={() => deleteRepository(repository.id)}
            className="danger"
          >
            Delete
          </button>
        )}
      </div>
    </div>
  );
}
```

**Vue.js Component:**
```vue
<template>
  <div class="repository-card">
    <h3>{{ repository.name }}</h3>
    <p>{{ repository.description }}</p>
    
    <div class="actions">
      <button v-if="canWrite" @click="editRepository">Edit</button>
      <button v-if="canAdmin" @click="managePermissions">Permissions</button>
      <button v-if="canDelete" @click="deleteRepository" class="danger">Delete</button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { Value } from 'oso-cloud';

export default {
  props: ['repository', 'userId'],
  setup(props) {
    const allowedActions = ref([]);

    const canWrite = computed(() => allowedActions.value.includes('write'));
    const canAdmin = computed(() => allowedActions.value.includes('admin'));
    const canDelete = computed(() => allowedActions.value.includes('delete'));

    onMounted(async () => {
      const user = new Value("User", props.userId);
      const repo = new Value("Repository", props.repository.id);
      allowedActions.value = await oso.actions(user, repo);
    });

    return {
      canWrite,
      canAdmin,
      canDelete,
      editRepository: () => console.log('Edit repository'),
      managePermissions: () => console.log('Manage permissions'),
      deleteRepository: () => console.log('Delete repository')
    };
  }
};
</script>
```

## Advanced Querying

For complex authorization queries, use the Query Builder API to construct sophisticated queries with multiple conditions.

```javascript
oso.buildQuery(predicate: [string, ...Value[]]): QueryBuilder
```

**Basic Query:**
```javascript
// Find all users who can admin a specific repository
const adminUsers = await oso.buildQuery(["allow", oso.var("user"), "admin", repo])
  .evaluate(oso.var("user"));

console.log("Admin users:", adminUsers);
```

**Complex Query with Conditions:**
```javascript
// Find all repositories in the "acme" organization that Alice can read
const acmeOrg = new Value("Organization", "acme");
const readableAcmeRepos = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .and(["parent", oso.var("repo"), acmeOrg])
  .evaluate(oso.var("repo"));

console.log("Readable acme repositories:", readableAcmeRepos);
```

**Query with Context Facts:**
```javascript
// Query with temporary context
const contextQuery = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .withContextFacts([["is_public", oso.var("repo")]])
  .evaluate(oso.var("repo"));
```

**Migration from v1:**
```javascript
// Old (v1)
const results = await oso.query("allow", alice, "read", oso.var("repo"));

// New (v2)
const results = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .evaluate(oso.var("repo"));
```

### Administrative Queries

```javascript
class AdminService {
  constructor(oso) {
    this.oso = oso;
  }

  async getRepositoryAdmins(repositoryId) {
    const repository = new Value("Repository", repositoryId);
    return await this.oso.buildQuery(["allow", this.oso.var("user"), "admin", repository])
      .evaluate(this.oso.var("user"));
  }

  async getUserRepositories(userId, action = "read") {
    const user = new Value("User", userId);
    return await this.oso.buildQuery(["allow", user, action, this.oso.var("repo")])
      .evaluate(this.oso.var("repo"));
  }

  async getUserPermissionMatrix(userId) {
    const user = new Value("User", userId);
    const actions = ["read", "write", "admin", "delete"];
    
    const matrix = {};
    for (const action of actions) {
      matrix[action] = await this.oso.buildQuery(["allow", user, action, this.oso.var("repo")])
        .evaluate(this.oso.var("repo"));
    }
    
    return matrix;
  }
}
```

## Context Facts

Context facts provide temporary, request-specific information for authorization decisions.

```javascript
// Time-based authorization
const contextFacts = [
  ["current_time", new Date().getHours()],
  ["business_hours", true]
];

const canAccessDuringBusinessHours = await oso.authorize(user, "access", resource, contextFacts);

// Location-based authorization
const locationFacts = [
  ["user_location", user, "office"],
  ["resource_requires_office_access", resource]
];

const canAccessFromOffice = await oso.authorize(user, "access", resource, locationFacts);

// Feature flag authorization
const featureFacts = [
  ["feature_enabled", "advanced_analytics", true],
  ["user_tier", user, "premium"]
];

const canUseAdvancedFeatures = await oso.authorize(user, "use_advanced_analytics", resource, featureFacts);
```

## Performance Tips

### Batch Authorization Checks

```javascript
// Instead of multiple individual checks
const canRead = await oso.authorize(alice, "read", repo1);
const canWrite = await oso.authorize(alice, "write", repo1);
const canAdmin = await oso.authorize(alice, "admin", repo1);

// Use actions() for multiple permission checks on same resource
const allowedActions = await oso.actions(alice, repo1);
const canRead = allowedActions.includes("read");
const canWrite = allowedActions.includes("write");
const canAdmin = allowedActions.includes("admin");
```

### Parallel Checks

```javascript
// Check multiple resources in parallel
const repositories = [repo1, repo2, repo3];
const authResults = await Promise.all(
  repositories.map(repo => oso.authorize(alice, "read", repo))
);

const accessibleRepos = repositories.filter((repo, index) => authResults[index]);
```

### Caching Results

```javascript
class CachedAuthorizationService {
  constructor(oso, cacheTimeout = 5 * 60 * 1000) { // 5 minutes
    this.oso = oso;
    this.cache = new Map();
    this.cacheTimeout = cacheTimeout;
  }

  async authorize(user, action, resource) {
    const key = `${user.id}:${action}:${resource.id}`;
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.result;
    }
    
    const result = await this.oso.authorize(user, action, resource);
    this.cache.set(key, { result, timestamp: Date.now() });
    
    return result;
  }
}
```

## Next Steps

- **[Manage facts](/reference/client-libraries/nodejs/facts-management)** - Store and retrieve authorization data
- **[Local authorization](/reference/client-libraries/nodejs/local-authorization)** - Database-level filtering for performance
- **[Error handling](/reference/client-libraries/nodejs/error-handling)** - Handle authorization failures gracefully
