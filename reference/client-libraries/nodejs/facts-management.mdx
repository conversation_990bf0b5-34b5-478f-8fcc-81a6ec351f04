---
title: Facts Management
description: "Store, retrieve, and manage authorization data with CRUD operations and batch processing."
sidebarTitle: "Facts Management"
---

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

## Insert Facts

Adds a single fact to the centralized authorization data store.

```javascript
oso.insert(fact: [string, ...Value[]]): Promise<void>
```

**Example:**
```javascript
import { Value } from 'oso-cloud';

// Create entity values
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");
const org = new Value("Organization", "acme");

// Insert role assignment
await oso.insert(["has_role", alice, "admin", repo]);

// Insert organization membership
await oso.insert(["member_of", alice, org]);

// Insert resource property
await oso.insert(["is_public", repo]);
```

**Migration from v1:**
```javascript
// Old (v1)
await oso.tell("has_role", alice, "admin", repo);

// New (v2)
await oso.insert(["has_role", alice, "admin", repo]);
```

### Real-World Examples

**User Registration:**
```javascript
class UserService {
  constructor(oso) {
    this.oso = oso;
  }

  async registerUser(userId, organizationId, role = "member") {
    const user = new Value("User", userId);
    const organization = new Value("Organization", organizationId);
    
    // Add user to organization with default role
    await this.oso.insert(["member_of", user, organization]);
    await this.oso.insert(["has_role", user, role, organization]);
    
    // Set user properties
    await this.oso.insert(["user_status", user, "active"]);
    await this.oso.insert(["joined_at", user, new Date().toISOString()]);
  }

  async promoteUser(userId, organizationId, newRole) {
    const user = new Value("User", userId);
    const organization = new Value("Organization", organizationId);
    
    // Remove old role and add new one
    await this.oso.delete(["has_role", user, null, organization]);
    await this.oso.insert(["has_role", user, newRole, organization]);
    
    // Log the promotion
    await this.oso.insert(["role_changed", user, organization, new Date().toISOString()]);
  }
}
```

## Batch Operations

Perform multiple fact operations atomically using transactions.

```javascript
oso.batch(callback: (tx: Transaction) => void): Promise<void>
```

**Example:**
```javascript
// Atomic batch operation
await oso.batch((tx) => {
  // Remove all existing roles for Alice
  tx.delete(["has_role", alice, null, null]);
  
  // Add new role assignments
  tx.insert(["has_role", alice, "admin", repo1]);
  tx.insert(["has_role", alice, "member", repo2]);
  tx.insert(["member_of", alice, org]);
});
```

### Complex Operations

```javascript
class RoleMigrationService {
  constructor(oso) {
    this.oso = oso;
  }

  async migrateUserRoles(userId, roleChanges) {
    const user = new Value("User", userId);
    
    await this.oso.batch((tx) => {
      for (const [resourceId, newRole] of Object.entries(roleChanges)) {
        const resource = new Value("Repository", resourceId);
        
        // Remove old role
        tx.delete(["has_role", user, null, resource]);
        
        // Add new role
        tx.insert(["has_role", user, newRole, resource]);
        
        // Log the change
        tx.insert(["role_migrated", user, resource, new Date().toISOString()]);
      }
    });
  }

  async transferRepositoryOwnership(repositoryId, fromUserId, toUserId) {
    const repository = new Value("Repository", repositoryId);
    const fromUser = new Value("User", fromUserId);
    const toUser = new Value("User", toUserId);
    
    await this.oso.batch((tx) => {
      // Remove old ownership
      tx.delete(["owns", fromUser, repository]);
      tx.delete(["has_role", fromUser, "admin", repository]);
      
      // Add new ownership
      tx.insert(["owns", toUser, repository]);
      tx.insert(["has_role", toUser, "admin", repository]);
      
      // Demote previous owner to member
      tx.insert(["has_role", fromUser, "member", repository]);
      
      // Log the transfer
      tx.insert(["ownership_transferred", repository, fromUser, toUser, new Date().toISOString()]);
    });
  }
}
```

## Get Facts

Retrieve facts from the authorization data store using pattern matching.

```javascript
oso.get(pattern: [string, ...Value[]]): Promise<Fact[]>
```

**Example:**
```javascript
// Get all roles for Alice
const aliceRoles = await oso.get(["has_role", alice, null, null]);
console.log("Alice's roles:", aliceRoles);

// Get all members of an organization
const orgMembers = await oso.get(["member_of", null, org]);
console.log("Organization members:", orgMembers);

// Get specific fact
const isPublic = await oso.get(["is_public", repo]);
console.log("Repository is public:", isPublic.length > 0);
```

### Query Patterns

```javascript
class UserProfileService {
  constructor(oso) {
    this.oso = oso;
  }

  async getUserProfile(userId) {
    const user = new Value("User", userId);
    
    // Get all organizations user belongs to
    const orgFacts = await this.oso.get(["member_of", user, null]);
    const organizations = orgFacts.map(fact => fact[2].id);
    
    // Get all repositories user has access to
    const roleFacts = await this.oso.get(["has_role", user, null, null]);
    const repositories = roleFacts
      .filter(fact => fact[3].type === "Repository")
      .reduce((acc, fact) => {
        acc[fact[3].id] = fact[2]; // resource_id -> role
        return acc;
      }, {});
    
    // Get user properties
    const statusFacts = await this.oso.get(["user_status", user, null]);
    const status = statusFacts.length > 0 ? statusFacts[0][2] : null;
    
    const joinedFacts = await this.oso.get(["joined_at", user, null]);
    const joinedAt = joinedFacts.length > 0 ? joinedFacts[0][2] : null;
    
    return {
      userId,
      organizations,
      repositories,
      status,
      joinedAt
    };
  }
}
```

## Delete Facts

Remove facts from the authorization data store.

```javascript
oso.delete(pattern: [string, ...Value[]]): Promise<void>
```

**Example:**
```javascript
// Delete specific role assignment
await oso.delete(["has_role", alice, "admin", repo]);

// Delete all roles for Alice on any repository
await oso.delete(["has_role", alice, null, null]);

// Delete all facts about a repository
await oso.delete([null, null, repo]);
```

### Cleanup Operations

```javascript
class UserManagementService {
  constructor(oso) {
    this.oso = oso;
  }

  async deactivateUser(userId) {
    const user = new Value("User", userId);
    
    // Remove all role assignments
    await this.oso.delete(["has_role", user, null, null]);
    
    // Remove organization memberships
    await this.oso.delete(["member_of", user, null]);
    
    // Remove ownership (should be transferred first)
    await this.oso.delete(["owns", user, null]);
    
    // Mark as deactivated
    await this.oso.insert(["user_status", user, "deactivated"]);
    await this.oso.insert(["deactivated_at", user, new Date().toISOString()]);
  }

  async deleteUser(userId) {
    const user = new Value("User", userId);
    
    // Delete all facts related to the user
    await this.oso.delete([null, user, null]);  // User as subject
    await this.oso.delete([null, null, user]);  // User as object
    
    // Clean up any remaining references
    const allFacts = await this.oso.get([null, null, null]);
    const userFacts = allFacts.filter(fact => 
      fact.some(arg => arg instanceof Value && arg.type === "User" && arg.id === userId)
    );
    
    for (const fact of userFacts) {
      await this.oso.delete(fact);
    }
  }
}
```

## Best Practices

### Fact Naming Conventions

```javascript
// Use consistent naming patterns
await oso.insert(["has_role", user, "admin", resource]);      // Role assignments
await oso.insert(["member_of", user, organization]);          // Memberships
await oso.insert(["owns", user, resource]);                   // Ownership
await oso.insert(["belongs_to", resource, organization]);     // Hierarchy
await oso.insert(["is_public", resource]);                    // Properties
await oso.insert(["created_at", resource, timestamp]);        // Timestamps
```

### Error Handling

```javascript
class SafeFactService {
  constructor(oso) {
    this.oso = oso;
  }

  async tryInsertFact(fact) {
    try {
      await this.oso.insert(fact);
      console.log("Inserted fact:", fact);
      return true;
    } catch (error) {
      console.error("Failed to insert fact:", fact, error);
      return false;
    }
  }

  async tryBatchOperation(callback) {
    try {
      await this.oso.batch(callback);
      console.log("Executed batch operation successfully");
      return true;
    } catch (error) {
      console.error("Failed to execute batch operation:", error);
      return false;
    }
  }
}
```

### Validation

```javascript
class ValidatedFactService {
  constructor(oso) {
    this.oso = oso;
  }

  async insertUserRole(userId, role, resourceId, resourceType) {
    // Validate inputs
    if (!userId) throw new Error("User ID cannot be empty");
    if (!role) throw new Error("Role cannot be empty");
    if (!resourceId) throw new Error("Resource ID cannot be empty");
    
    const validRoles = ["admin", "member", "viewer"];
    if (!validRoles.includes(role)) {
      throw new Error(`Invalid role: ${role}`);
    }
    
    const user = new Value("User", userId);
    const resource = new Value(resourceType, resourceId);
    
    await this.oso.insert(["has_role", user, role, resource]);
  }
}
```

## Next Steps

- **[Policy management](/reference/client-libraries/nodejs/policy-management)** - Deploy and manage authorization policies
- **[Local authorization](/reference/client-libraries/nodejs/local-authorization)** - Database-level filtering for performance
- **[Testing](/reference/client-libraries/nodejs/testing)** - Test your facts and authorization logic
