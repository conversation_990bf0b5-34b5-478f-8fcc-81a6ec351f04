---
title: Policy Management
description: "Deploy and manage Polar authorization policies in Oso Cloud."
sidebarTitle: "Policy Management"
---

Deploy and manage your Polar authorization policies in Oso Cloud.

## Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```javascript
await oso.policy(policy: string): Promise<void>
```

**Example:**
```javascript
try {
  await oso.policy(`
    actor User {}
    resource Repository {
      permissions = ["read", "write", "admin"];
      roles = ["member", "maintainer", "admin"];
      
      "member" if "maintainer";
      "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
      has_role(user, "member", repo);
      
    allow(user: User, "write", repo: Repository) if
      has_role(user, "maintainer", repo);
      
    allow(user: User, "admin", repo: Repository) if
      has_role(user, "admin", repo);
      
    test "basic permissions" {
      setup {
        has_role(User{"alice"}, "member", Repository{"widgets"});
      }
      
      assert allow(User{"alice"}, "read", Repository{"widgets"});
      assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
  `);
  
  console.log("Policy deployed successfully!");
} catch (error) {
  console.error("Policy deployment failed:", error.message);
}
```

## Get Policy

Retrieve the currently deployed policy.

```javascript
oso.getPolicy(): Promise<string>
```

**Example:**
```javascript
const currentPolicy = await oso.getPolicy();
console.log("Current policy:", currentPolicy);
```

## Policy Stats

Get statistics about your deployed policy.

```javascript
oso.stats(): Promise<PolicyStats>
```

**Example:**
```javascript
const stats = await oso.stats();
console.log("Policy statistics:", {
  numRules: stats.num_rules,
  numTypes: stats.num_types,
  numResources: stats.num_resources
});
```

## Next Steps

- **[Local authorization](/reference/client-libraries/nodejs/local-authorization)** - Database-level filtering for performance
- **[Error handling](/reference/client-libraries/nodejs/error-handling)** - Handle policy deployment failures
