---
title: Installation & Setup
description: "Install and configure the Oso Cloud Node.js client library in your application."
sidebarTitle: "Installation"
---

## Installation

Install the Oso Cloud Node.js client:

```bash
npm install oso-cloud
```

**Requirements:**
- Node.js 16 or later
- TypeScript support included

## Basic Setup

Initialize the Oso Cloud client with your API key:

```javascript
import { Oso } from 'oso-cloud';

const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH
});
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```javascript
const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH,
  timeout: 30000,  // Request timeout in milliseconds
  retries: 3       // Number of retry attempts
});
```

## TypeScript Setup

The Oso Cloud client includes full TypeScript definitions:

```typescript
import { Oso, Value, Fact } from 'oso-cloud';

const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH!
});

// Type-safe value creation
const user: Value = new Value("User", "alice");
const repository: Value = new Value("Repository", "acme/widgets");

// Type-safe authorization
const allowed: boolean = await oso.authorize(user, "read", repository);
```

### Generate Types from Policies

Generate TypeScript types from your Polar policies:

```bash
# Install CLI
npm install -g oso-cloud-cli

# Generate types
oso-cloud generate-types --output ./src/types/oso-types.ts
```

```typescript
import { User, Repository } from './types/oso-types';

const user: User = { type: "User", id: "alice" };
const repo: Repository = { type: "Repository", id: "acme/widgets" };

// TypeScript will enforce correct types
const allowed = await oso.authorize(user, "read", repo);
```

## Framework Integration

### Express.js

```javascript
import express from 'express';
import { Oso, Value } from 'oso-cloud';

const app = express();
const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH
});

// Authorization middleware
const authorize = (action) => {
  return async (req, res, next) => {
    try {
      const user = new Value("User", req.user.id);
      const resource = new Value("Repository", req.params.repoId);
      
      const allowed = await oso.authorize(user, action, resource);
      if (allowed) {
        next();
      } else {
        res.status(403).json({ error: "Forbidden" });
      }
    } catch (error) {
      res.status(500).json({ error: "Authorization check failed" });
    }
  };
};

// Protected routes
app.get('/repositories/:repoId', authorize('read'), (req, res) => {
  // Handle request
});

app.put('/repositories/:repoId', authorize('write'), (req, res) => {
  // Handle request
});
```

### Next.js

**API Routes:**
```javascript
// pages/api/repositories/[id].js
import { Oso, Value } from 'oso-cloud';

const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH
});

export default async function handler(req, res) {
  try {
    const user = new Value("User", req.user.id);
    const repository = new Value("Repository", req.query.id);
    
    const allowed = await oso.authorize(user, "read", repository);
    if (!allowed) {
      return res.status(403).json({ error: "Access denied" });
    }
    
    // Handle request
    const repo = await getRepository(req.query.id);
    res.json(repo);
  } catch (error) {
    res.status(500).json({ error: "Internal server error" });
  }
}
```

**App Router (Next.js 13+):**
```typescript
// app/api/repositories/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Oso, Value } from 'oso-cloud';

const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH!
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = new Value("User", "current-user-id"); // Get from auth
    const repository = new Value("Repository", params.id);
    
    const allowed = await oso.authorize(user, "read", repository);
    if (!allowed) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 });
    }
    
    const repo = await getRepository(params.id);
    return NextResponse.json(repo);
  } catch (error) {
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
```

### Fastify

```javascript
import Fastify from 'fastify';
import { Oso, Value } from 'oso-cloud';

const fastify = Fastify();

// Register Oso plugin
fastify.register(async function (fastify) {
  const oso = new Oso({
    url: "https://cloud.osohq.com",
    apiKey: process.env.OSO_AUTH
  });
  
  fastify.decorate('oso', oso);
  
  // Authorization decorator
  fastify.decorate('authorize', async function (user, action, resource) {
    return await this.oso.authorize(user, action, resource);
  });
});

// Protected route
fastify.get('/repositories/:id', async (request, reply) => {
  const user = new Value("User", request.user.id);
  const repository = new Value("Repository", request.params.id);
  
  const allowed = await fastify.authorize(user, "read", repository);
  if (!allowed) {
    reply.code(403).send({ error: "Forbidden" });
    return;
  }
  
  // Handle request
});
```

### NestJS

**Module Setup:**
```typescript
// oso.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Oso } from 'oso-cloud';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'OSO_CLIENT',
      useFactory: (configService: ConfigService) => {
        return new Oso({
          url: "https://cloud.osohq.com",
          apiKey: configService.get('OSO_AUTH')
        });
      },
      inject: [ConfigService],
    },
  ],
  exports: ['OSO_CLIENT'],
})
export class OsoModule {}
```

**Service:**
```typescript
// authorization.service.ts
import { Injectable, Inject } from '@nestjs/common';
import { Oso, Value } from 'oso-cloud';

@Injectable()
export class AuthorizationService {
  constructor(@Inject('OSO_CLIENT') private readonly oso: Oso) {}

  async authorize(userId: string, action: string, resourceType: string, resourceId: string): Promise<boolean> {
    const user = new Value("User", userId);
    const resource = new Value(resourceType, resourceId);
    
    return await this.oso.authorize(user, action, resource);
  }

  async listResources(userId: string, action: string, resourceType: string): Promise<string[]> {
    const user = new Value("User", userId);
    return await this.oso.list(user, action, resourceType);
  }
}
```

**Guard:**
```typescript
// authorization.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthorizationService } from './authorization.service';

@Injectable()
export class AuthorizationGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private authorizationService: AuthorizationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const action = this.reflector.get<string>('action', context.getHandler());
    const resourceType = this.reflector.get<string>('resourceType', context.getHandler());
    
    if (!action || !resourceType) {
      return true; // No authorization required
    }

    const request = context.switchToHttp().getRequest();
    const userId = request.user?.id;
    const resourceId = request.params?.id;

    if (!userId) {
      throw new ForbiddenException('User not authenticated');
    }

    const allowed = await this.authorizationService.authorize(userId, action, resourceType, resourceId);
    if (!allowed) {
      throw new ForbiddenException('Access denied');
    }

    return true;
  }
}
```

## Environment Configuration

### Development
```bash
# .env.development
OSO_AUTH=your-development-api-key
OSO_URL=https://cloud.osohq.com
```

### Production
```bash
# .env.production
OSO_AUTH=your-production-api-key
OSO_URL=https://cloud.osohq.com
```

### Docker
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .

# Set environment variables
ENV NODE_ENV=production
ENV OSO_URL=https://cloud.osohq.com

EXPOSE 3000
CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - OSO_AUTH=${OSO_AUTH}
      - OSO_URL=https://cloud.osohq.com
    env_file:
      - .env
```

## Testing Setup

```javascript
// test/setup.js
import { jest } from '@jest/globals';

// Mock Oso client for testing
jest.mock('oso-cloud', () => ({
  Oso: jest.fn().mockImplementation(() => ({
    authorize: jest.fn().mockResolvedValue(true),
    list: jest.fn().mockResolvedValue([]),
    actions: jest.fn().mockResolvedValue([]),
    insert: jest.fn().mockResolvedValue(undefined),
    delete: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue([]),
    batch: jest.fn().mockResolvedValue(undefined),
    policy: jest.fn().mockResolvedValue(undefined),
    getPolicy: jest.fn().mockResolvedValue(""),
    stats: jest.fn().mockResolvedValue({ num_rules: 0, num_types: 0, num_resources: 0 })
  })),
  Value: jest.fn().mockImplementation((type, id) => ({ type, id }))
}));
```

## Next Steps

- **[Learn authorization basics](/reference/client-libraries/nodejs/authorization-checks)** - Master the core authorization methods
- **[Manage facts](/reference/client-libraries/nodejs/facts-management)** - Store and retrieve authorization data
- **[Deploy policies](/reference/client-libraries/nodejs/policy-management)** - Manage your authorization policies
