---
title: Migration Guide
description: "Upgrade from v1 to v2 with breaking changes and new features."
sidebarTitle: "Migration Guide"
---

## Breaking Changes (v1 → v2)

### Node.js Version Requirement
Upgrade your Node.js runtime to version 16 or later.

```json
{
  "engines": {
    "node": ">=16.0.0"
  }
}
```

### Facts API Changes
All fact operations now use array-wrapped arguments instead of individual parameters.

```javascript
// New insert() method with array wrapping
await oso.insert(["has_role", user, "admin", repo]);

// New delete() method with array wrapping
await oso.delete(["has_role", user, "admin", repo]);

// New get() method with array wrapping
const facts = await oso.get(["has_role", user, null, null]);
```

### Batch Operations
The `bulk()` method has been replaced with a transaction-based `batch()` API.

```javascript
await oso.batch((tx) => {
  tx.delete(["has_role", user, null, null]);
  tx.insert(["has_role", user, "admin", repo]);
});
```

### Query API
The `query()` method has been replaced with `buildQuery().evaluate()`.

```javascript
const results = await oso.buildQuery(["has_role", user, "admin", repo])
  .evaluate();
```

## New Features

### TypeScript Type Generation
Generate TypeScript types from your Polar policies:

```bash
# Install CLI
npm install -g oso-cloud-cli

# Generate types
oso-cloud generate-types --output ./src/types/oso-types.ts
```

```typescript
import { User, Repository } from './types/oso-types';

const user: User = { type: "User", id: "alice" };
const repo: Repository = { type: "Repository", id: "acme/widgets" };

// TypeScript will enforce correct types
const allowed = await oso.authorize(user, "read", repo);
```

### Enhanced QueryBuilder
Complex queries with fluent interface and context facts:

```javascript
const users = await oso.buildQuery(["allow", oso.var("user"), "admin", oso.var("repo")])
  .and(["parent", oso.var("repo"), organization])
  .withContextFacts([["is_public", oso.var("repo")]])
  .evaluate(oso.var("user"));
```

## Next Steps

- **[Performance optimization](/reference/client-libraries/nodejs/performance)** - Improve authorization performance
- **[Testing strategies](/reference/client-libraries/nodejs/testing)** - Test your authorization logic
