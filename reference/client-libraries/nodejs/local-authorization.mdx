---
title: Local Authorization
description: "Database-level filtering with SQL generation for high-performance authorization."
sidebarTitle: "Local Authorization"
---

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

## List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```javascript
oso.listLocal(actor: Value, action: string, resourceType: string, column: string): Promise<string>
```

**Basic Example:**
```javascript
import { Value } from 'oso-cloud';

const alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
const sqlFilter = await oso.listLocal(alice, "read", "Repository", "id");
console.log("SQL Filter:", sqlFilter);
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Kysely):**
```javascript
import { Kysely } from 'kysely';

async function getReadableRepositories(userId) {
  const user = new Value("User", userId);
  const filter = await oso.listLocal(user, "read", "Repository", "id");
  
  // Use the filter in your database query
  const repositories = await db
    .selectFrom('repositories')
    .selectAll()
    .where(sql`${sql.raw(filter)}`)
    .execute();
    
  return repositories;
}
```

**Advanced Usage:**
```javascript
// Get repositories Alice can admin with additional filtering
const adminFilter = await oso.listLocal(alice, "admin", "Repository", "id");
const repositories = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(adminFilter)}`)
  .where('active', '=', true)
  .where('created_at', '>', new Date('2023-01-01'))
  .execute();
```

## Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```javascript
oso.authorizeLocal(actor: Value, action: string, resource: Value, column: string): Promise<string>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get SQL condition for checking if Alice can read the repository
const condition = await oso.authorizeLocal(alice, "read", repo, "id");
console.log("SQL Condition:", condition);
// Output: "id = 'acme/widgets'"

// Use in a database query
const repository = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(condition)}`)
  .executeTakeFirst();
```

## Next Steps

- **[Performance tips](/reference/client-libraries/nodejs/performance)** - Optimize your authorization queries
- **[Testing](/reference/client-libraries/nodejs/testing)** - Test your local authorization logic
