---
title: Node.js Client Library
description: "Complete guide to using the Oso Cloud Node.js client library for authorization."
sidebarTitle: "Node.js"
---

The Oso Cloud Node.js client library provides modern authorization capabilities with TypeScript support, async/await patterns, and comprehensive error handling.

## Quick Start

```javascript
import { Oso } from 'oso-cloud';

const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH
});

const user = new Value("User", "alice");
const resource = new Value("Repository", "acme/widgets");

// Check authorization
const allowed = await oso.authorize(user, "read", resource);
console.log(`Access allowed: ${allowed}`);
```

## What's Included

<CardGroup cols={2}>
  <Card
    title="Installation & Setup"
    icon="download"
    href="/reference/client-libraries/nodejs/installation"
  >
    Install via npm and configure your Node.js application
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/nodejs/authorization-checks"
  >
    Core authorization methods with async/await support
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/nodejs/facts-management"
  >
    CRUD operations for authorization data with transactions
  </Card>
  
  <Card
    title="Policy Management"
    icon="file-code"
    href="/reference/client-libraries/nodejs/policy-management"
  >
    Deploy and manage Polar authorization policies
  </Card>
  
  <Card
    title="Local Authorization"
    icon="server"
    href="/reference/client-libraries/nodejs/local-authorization"
  >
    Database-level filtering with SQL generation
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/nodejs/migration-guide"
  >
    Upgrade from v1 to v2 with breaking changes guide
  </Card>
  
  <Card
    title="Error Handling"
    icon="exclamation-triangle"
    href="/reference/client-libraries/nodejs/error-handling"
  >
    Exception handling and error recovery patterns
  </Card>
  
  <Card
    title="Performance"
    icon="gauge-high"
    href="/reference/client-libraries/nodejs/performance"
  >
    Optimization tips, caching, and async patterns
  </Card>
</CardGroup>

## Key Features

### TypeScript Support
Full TypeScript definitions with type generation from policies:

```typescript
import { User, Repository } from './types/oso-types';

const user: User = { type: "User", id: "alice" };
const repo: Repository = { type: "Repository", id: "acme/widgets" };

const allowed = await oso.authorize(user, "read", repo);
```

### Modern Async/Await
All methods return promises for seamless async integration:

```javascript
// Sequential checks
const canRead = await oso.authorize(user, "read", resource);
const canWrite = await oso.authorize(user, "write", resource);

// Parallel checks
const [readAccess, writeAccess] = await Promise.all([
  oso.authorize(user, "read", resource),
  oso.authorize(user, "write", resource)
]);
```

### Query Builder API
Powerful query building with fluent interface:

```javascript
const results = await oso.buildQuery(["allow", oso.var("user"), "admin", repo])
  .and(["parent", oso.var("repo"), organization])
  .withContextFacts([["is_public", oso.var("repo")]])
  .evaluate(oso.var("user"));
```

## Framework Integration

### Express.js
```javascript
app.use(async (req, res, next) => {
  const user = new Value("User", req.user.id);
  const resource = new Value("Repository", req.params.repoId);
  
  if (await oso.authorize(user, req.method.toLowerCase(), resource)) {
    next();
  } else {
    res.status(403).json({ error: "Forbidden" });
  }
});
```

### Next.js
```javascript
// pages/api/repositories/[id].js
export default async function handler(req, res) {
  const user = new Value("User", req.user.id);
  const repository = new Value("Repository", req.query.id);
  
  if (!await oso.authorize(user, "read", repository)) {
    return res.status(403).json({ error: "Access denied" });
  }
  
  // Handle request
}
```

## Next Steps

1. **[Install the package](/reference/client-libraries/nodejs/installation)** - Get started in minutes
2. **[Learn authorization basics](/reference/client-libraries/nodejs/authorization-checks)** - Master core concepts
3. **[Explore examples](https://github.com/osohq/oso-cloud-nodejs-examples)** - See real implementations
