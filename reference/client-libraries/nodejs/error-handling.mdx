---
title: <PERSON>rror Handling
description: "Exception handling and error recovery patterns for robust Node.js applications."
sidebarTitle: "Error Handling"
---

## Error Types

```javascript
try {
  const allowed = await oso.authorize(user, action, resource);
  return allowed;
} catch (error) {
  if (error.code === 'POLICY_NOT_FOUND') {
    console.log('No policy deployed');
    return false; // Fail closed
  } else if (error.code === 'INVALID_ACTOR') {
    console.log('Invalid user format');
    throw new Error('Authentication required');
  } else {
    console.error('Authorization check failed:', error);
    throw error; // Re-throw unexpected errors
  }
}
```

## Logging and Monitoring

```javascript
class AuthorizationService {
  constructor(oso, logger) {
    this.oso = oso;
    this.logger = logger;
  }

  async authorize(user, action, resource) {
    try {
      const result = await this.oso.authorize(user, action, resource);
      this.logger.debug('Authorization check', { 
        user: user.id, 
        action, 
        resource: resource.id, 
        result 
      });
      return result;
    } catch (error) {
      this.logger.error('Authorization failed', { 
        user: user.id, 
        action, 
        resource: resource.id, 
        error: error.message 
      });
      throw error;
    }
  }
}
```

## Next Steps

- **[Performance optimization](/reference/client-libraries/nodejs/performance)** - Improve authorization performance
- **[Testing strategies](/reference/client-libraries/nodejs/testing)** - Test error scenarios
