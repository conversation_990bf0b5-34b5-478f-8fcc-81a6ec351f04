---
title: Ruby Client Library
description: "Complete guide to using the Oso Cloud Ruby client library for authorization."
sidebarTitle: "Ruby"
---

## Installation

Install the Oso Cloud Ruby gem:

```bash
gem install oso-cloud
```

Or add to your Gemfile:
```ruby
gem 'oso-cloud'
```

**Requirements:**
- Ruby 3.0 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```ruby
require 'oso-cloud'

oso = OsoCloud::Client.new(
  url: "https://cloud.osohq.com",
  api_key: ENV["OSO_AUTH"]
)
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```ruby
oso = OsoCloud::Client.new(
  url: "https://cloud.osohq.com",
  api_key: ENV["OSO_AUTH"],
  timeout: 30,  # Request timeout in seconds
  retries: 3    # Number of retry attempts
)
```

## Authorization Checks

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

### Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```ruby
oso.authorize(actor, action, resource, context_facts = nil)
```

**Basic Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Check if Alice can read the repository
can_read = oso.authorize(alice, "read", repo)
puts "Alice can read repo: #{can_read}"

# Check if Alice can write to the repository
can_write = oso.authorize(alice, "write", repo)
puts "Alice can write to repo: #{can_write}"
```

**With Context Facts:**
```ruby
# Check authorization with temporary context
can_read_public = oso.authorize(alice, "read", repo, [["is_public", repo]])
```

### List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```ruby
oso.list(actor, action, resource_type, context_facts = nil)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }

# Get all repositories Alice can read
readable_repos = oso.list(alice, "read", "Repository")
puts "Readable repositories: #{readable_repos}"

# Get all organizations Alice can admin
admin_orgs = oso.list(alice, "admin", "Organization")
puts "Admin organizations: #{admin_orgs}"
```

**With Context Facts:**
```ruby
# List with additional context
public_repos = oso.list(alice, "read", "Repository", [["user_preference", alice, "show_public", true]])
```

### List Actions

Fetches all actions that an actor can perform on a specific resource.

```ruby
oso.actions(actor, resource, context_facts = nil)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Get all actions Alice can perform on the repository
allowed_actions = oso.actions(alice, repo)
puts "Allowed actions: #{allowed_actions}"
# Output: ["read", "write"] or ["read", "write", "admin"]
```

### Advanced Querying

For complex authorization queries, use the simple query interface.

```ruby
oso.query(rule)
```

**Basic Query:**
```ruby
# Find all users who can admin a specific repository
admin_users = oso.query(["allow", { "type" => "User" }, "admin", repo])

puts "Admin users: #{admin_users}"
```

**Complex Query with Type Constraints:**
```ruby
# Find all repositories in the "acme" organization that Alice can read
readable_acme_repos = oso.query([
  "allow", 
  alice, 
  "read", 
  { "type" => "Repository", "parent" => { "type" => "Organization", "id" => "acme" } }
])

puts "Readable acme repositories: #{readable_acme_repos}"
```

**Note:** Ruby uses a simple query interface without the QueryBuilder pattern.

## Facts Management

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

### Insert Facts

Adds a single fact to the centralized authorization data store.

```ruby
oso.insert(fact)
```

**Example:**
```ruby
# Create entity values
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }
org = { type: "Organization", id: "acme" }

# Insert role assignment
oso.insert(["has_role", alice, "admin", repo])

# Insert organization membership
oso.insert(["member_of", alice, org])

# Insert resource property
oso.insert(["is_public", repo])
```

### Batch Operations

Perform multiple fact operations atomically using transactions.

```ruby
oso.batch do |tx|
  # Remove all existing roles for Alice
  tx.delete(["has_role", alice, nil, nil])
  
  # Add new role assignments
  tx.insert(["has_role", alice, "admin", repo1])
  tx.insert(["has_role", alice, "member", repo2])
  tx.insert(["member_of", alice, org])
end
```

### Get Facts

Retrieve facts from the authorization data store using pattern matching.

```ruby
oso.get(pattern)
```

**Example:**
```ruby
# Get all roles for Alice
alice_roles = oso.get(["has_role", alice, nil, nil])
puts "Alice's roles: #{alice_roles}"

# Get all members of an organization
org_members = oso.get(["member_of", nil, org])
puts "Organization members: #{org_members}"

# Get specific fact
is_public = oso.get(["is_public", repo])
puts "Repository is public: #{!is_public.empty?}"
```

### Delete Facts

Remove facts from the authorization data store.

```ruby
oso.delete(pattern)
```

**Example:**
```ruby
# Delete specific role assignment
oso.delete(["has_role", alice, "admin", repo])

# Delete all roles for Alice on any repository
oso.delete(["has_role", alice, nil, nil])

# Delete all facts about a repository
oso.delete([nil, nil, repo])
```

## Policy Management

Deploy and manage your Polar authorization policies in Oso Cloud.

### Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```ruby
oso.policy(policy_string)
```

**Example:**
```ruby
begin
  policy = <<~POLAR
    actor User {}
    resource Repository {
      permissions = ["read", "write", "admin"];
      roles = ["member", "maintainer", "admin"];
      
      "member" if "maintainer";
      "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
      has_role(user, "member", repo);
      
    allow(user: User, "write", repo: Repository) if
      has_role(user, "maintainer", repo);
      
    allow(user: User, "admin", repo: Repository) if
      has_role(user, "admin", repo);
      
    test "basic permissions" {
      setup {
        has_role(User{"alice"}, "member", Repository{"widgets"});
      }
      
      assert allow(User{"alice"}, "read", Repository{"widgets"});
      assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
  POLAR
  
  oso.policy(policy)
  puts "Policy deployed successfully!"
rescue => error
  puts "Policy deployment failed: #{error.message}"
end
```

### Get Policy

Retrieve the currently deployed policy.

```ruby
oso.get_policy
```

**Example:**
```ruby
current_policy = oso.get_policy
puts "Current policy: #{current_policy}"
```

### Policy Stats

Get statistics about your deployed policy.

```ruby
oso.stats
```

**Example:**
```ruby
stats = oso.stats
puts "Policy statistics: #{stats.inspect}"
```

## Local Authorization

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

### List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```ruby
oso.list_local(actor, action, resource_type, column)
```

**Basic Example:**
```ruby
alice = { type: "User", id: "alice" }

# Get SQL filter for repositories Alice can read
sql_filter = oso.list_local(alice, "read", "Repository", "id")
puts "SQL Filter: #{sql_filter}"
# Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (ActiveRecord):**
```ruby
class Repository < ApplicationRecord
  def self.readable_by(user)
    filter = $oso.list_local(
      { type: "User", id: user.id }, 
      "read", 
      "Repository", 
      "id"
    )
    
    where(filter)
  end
end

# Usage
user = User.find(1)
readable_repos = Repository.readable_by(user)
```

**Advanced Usage:**
```ruby
# Get repositories Alice can admin with additional filtering
admin_filter = oso.list_local(alice, "admin", "Repository", "id")

Repository.where(admin_filter)
          .where(active: true)
          .where("created_at > ?", Date.new(2023, 1, 1))
```

### Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```ruby
oso.authorize_local(actor, action, resource, column)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Get SQL condition for checking if Alice can read the repository
condition = oso.authorize_local(alice, "read", repo, "id")
puts "SQL Condition: #{condition}"
# Output: "id = 'acme/widgets'"

# Use in a database query
repository = Repository.where(condition).first
```

## Migration Guide

### Stable API

Ruby client library has a stable API with no breaking changes. The current version provides:

- **Consistent Interface**: All methods maintain backward compatibility
- **Simple Data Structures**: Uses native Ruby hashes and arrays
- **Idiomatic Ruby**: Follows Ruby conventions and patterns

### Best Practices

#### Error Handling
```ruby
begin
  allowed = oso.authorize(user, action, resource)
  return allowed
rescue OsoCloud::PolicyNotFoundError
  puts "No policy deployed"
  return false  # Fail closed
rescue OsoCloud::InvalidActorError
  puts "Invalid user format"
  raise ArgumentError, "Authentication required"
rescue OsoCloud::Error => error
  puts "Authorization check failed: #{error.message}"
  raise error  # Re-throw unexpected errors
end
```

#### Using with Rails
```ruby
# config/initializers/oso.rb
$oso = OsoCloud::Client.new(
  url: "https://cloud.osohq.com",
  api_key: Rails.application.credentials.oso_auth
)

# app/controllers/application_controller.rb
class ApplicationController < ActionController::Base
  private
  
  def authorize_action!(action, resource)
    user = { type: "User", id: current_user.id }
    resource_obj = { type: resource.class.name, id: resource.id }
    
    unless $oso.authorize(user, action, resource_obj)
      raise ActionController::Forbidden, "Not authorized"
    end
  end
end

# app/controllers/repositories_controller.rb
class RepositoriesController < ApplicationController
  before_action :set_repository, only: [:show, :edit, :update, :destroy]
  before_action -> { authorize_action!("read", @repository) }, only: [:show]
  before_action -> { authorize_action!("write", @repository) }, only: [:edit, :update]
  before_action -> { authorize_action!("admin", @repository) }, only: [:destroy]
  
  def index
    user = { type: "User", id: current_user.id }
    @repositories = Repository.readable_by(user)
  end
  
  private
  
  def set_repository
    @repository = Repository.find(params[:id])
  end
end
```

#### Caching
```ruby
require 'redis'

class CachedAuthorizationService
  def initialize(oso_client, redis_client = Redis.new)
    @oso = oso_client
    @redis = redis_client
  end
  
  def authorize(user, action, resource, ttl: 300)
    key = "auth:#{user[:id]}:#{action}:#{resource[:id]}"
    
    cached = @redis.get(key)
    return cached == "true" if cached
    
    result = @oso.authorize(user, action, resource)
    @redis.setex(key, ttl, result.to_s)
    result
  end
end

# Usage
cached_oso = CachedAuthorizationService.new($oso)
allowed = cached_oso.authorize(user, "read", repository)
```

## Performance Considerations

### Connection Management
```ruby
# Use connection pooling for high-traffic applications
require 'connection_pool'

OSO_POOL = ConnectionPool.new(size: 10, timeout: 5) do
  OsoCloud::Client.new(
    url: "https://cloud.osohq.com",
    api_key: ENV["OSO_AUTH"]
  )
end

# Usage
OSO_POOL.with do |oso|
  oso.authorize(user, action, resource)
end
```

### Batch Operations
```ruby
# Instead of multiple individual checks
can_read = oso.authorize(alice, "read", repo1)
can_write = oso.authorize(alice, "write", repo1)
can_admin = oso.authorize(alice, "admin", repo1)

# Use actions() for multiple permission checks on same resource
allowed_actions = oso.actions(alice, repo1)
can_read = allowed_actions.include?("read")
can_write = allowed_actions.include?("write")
can_admin = allowed_actions.include?("admin")
```

### Background Jobs
```ruby
# For Sidekiq workers
class AuthorizationWorker
  include Sidekiq::Worker
  
  def perform(user_id, action, resource_type, resource_id)
    user = { type: "User", id: user_id }
    resource = { type: resource_type, id: resource_id }
    
    allowed = $oso.authorize(user, action, resource)
    
    if allowed
      # Perform authorized action
      process_authorized_action(user, action, resource)
    else
      # Log unauthorized attempt
      Rails.logger.warn "Unauthorized attempt: #{user_id} -> #{action} on #{resource_type}:#{resource_id}"
    end
  end
end
```

## Testing

### RSpec Integration
```ruby
# spec/support/oso_helpers.rb
module OsoHelpers
  def mock_oso_authorize(user, action, resource, result: true)
    allow($oso).to receive(:authorize)
      .with(user, action, resource)
      .and_return(result)
  end
  
  def expect_oso_authorize(user, action, resource)
    expect($oso).to receive(:authorize)
      .with(user, action, resource)
      .and_return(true)
  end
end

RSpec.configure do |config|
  config.include OsoHelpers
end

# spec/controllers/repositories_controller_spec.rb
RSpec.describe RepositoriesController, type: :controller do
  let(:user) { { type: "User", id: "alice" } }
  let(:repository) { { type: "Repository", id: "repo1" } }
  
  describe "GET #show" do
    it "allows access when authorized" do
      mock_oso_authorize(user, "read", repository, result: true)
      
      get :show, params: { id: "repo1" }
      
      expect(response).to have_http_status(:success)
    end
    
    it "denies access when not authorized" do
      mock_oso_authorize(user, "read", repository, result: false)
      
      expect {
        get :show, params: { id: "repo1" }
      }.to raise_error(ActionController::Forbidden)
    end
  end
end
```

## Next Steps

- [Policy Patterns](/develop/policies/patterns) - Learn common authorization patterns
- [Performance Guide](/develop/troubleshooting/query-performance) - Optimize authorization performance
- [HTTP API Reference](/reference/api/check-api) - Direct API access
