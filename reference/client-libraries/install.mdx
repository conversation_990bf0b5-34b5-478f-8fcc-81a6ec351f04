---
title: Installation and Setup
description: "This guide will help you get started with the Oso Cloud client libraries across all supported languages."
sidebarTitle: "Install"
---

## Installation

<Tabs>
<Tab title="Node.js">

Install the Oso Cloud Node.js client:

```bash
npm install oso-cloud
```

**Requirements:**
- Node.js 16 or later
- TypeScript support included

</Tab>
<Tab title="Python">

Install the Oso Cloud Python client:

```bash
pip install oso-cloud
```

**Requirements:**
- Python 3.8 or later

</Tab>
<Tab title="Go">

Install the Oso Cloud Go client:

```bash
go get github.com/osohq/go-oso-cloud/v2
```

**Requirements:**
- Go 1.19 or later

</Tab>
<Tab title="Java">

Add the Oso Cloud Java client to your project:

**Maven:**
```xml
<dependency>
    <groupId>com.osohq</groupId>
    <artifactId>oso-cloud</artifactId>
    <version>1.0.0</version>
</dependency>
```

**Gradle:**
```groovy
implementation 'com.osohq:oso-cloud:1.0.0'
```

**Requirements:**
- Java 11 or later

</Tab>
<Tab title="Ruby">

Install the Oso Cloud Ruby gem:

```bash
gem install oso-cloud
```

Or add to your Gemfile:
```ruby
gem 'oso-cloud'
```

**Requirements:**
- Ruby 3.0 or later

</Tab>
<Tab title=".NET">

Install the Oso Cloud .NET package:

```bash
dotnet add package OsoCloud
```

**Requirements:**
- .NET 6.0 or later
</Tab>
</Tabs>

## Basic Setup

<Tabs>
<Tab title="Node.js" default>

```javascript
import { OsoCloud } from 'oso-cloud';

// Initialize the client with your API token
const oso = new OsoCloud({
  url: 'https://cloud.osohq.com',
  apiKey: process.env.OSO_CLOUD_API_KEY
});
```
</Tab>
<Tab title="Python">

```python
from oso_cloud import OsoCloud

# Initialize the client with your API token
oso = OsoCloud(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_CLOUD_API_KEY"]
)
```
</Tab>
<Tab title="Go">

```go
import (
    oso "github.com/osohq/go-oso-cloud/v2"
)

// Initialize the client with your API token
osoClient, err := oso.NewClient(oso.WithAPIKey(os.Getenv("OSO_CLOUD_API_KEY")))
if err != nil {
    log.Fatal(err)
}
defer osoClient.Close()
```
</Tab>
<Tab title="Java">

```java
import com.osohq.oso_cloud.OsoCloud;

// Initialize the client with your API token
OsoCloud oso = new OsoCloud(
    "https://cloud.osohq.com",
    System.getenv("OSO_CLOUD_API_KEY")
);
```
</Tab>
<Tab title="Ruby">

```ruby
require 'oso-cloud'

# Initialize the client with your API token
oso = OsoCloud::Client.new(
  url: 'https://cloud.osohq.com',
  api_key: ENV['OSO_CLOUD_API_KEY']
)
```

</Tab>
<Tab title=".NET">

```csharp
using OsoCloud;

// Initialize the client with your API token
var oso = new OsoCloudClient(
    "https://cloud.osohq.com",
    Environment.GetEnvironmentVariable("OSO_CLOUD_API_KEY")
);
```

</Tab>
</Tabs>

## Environment Setup
```bash
export OSO_CLOUD_API_KEY="your-api-key-here"
```


## Quickstart API Calls

<Tabs>
<Tab title="Node.js" default>

```javascript
async function quickStart() {
  try {
    // Deploy a simple policy
    await oso.policy(`
      actor User {}
      resource Repository {}
      
      allow(user: User, "read", repo: Repository) if
        has_role(user, "member", repo);
    `);

    // Create some entities
    const alice = { type: "User", id: "alice" };
    const repo = { type: "Repository", id: "acme/widgets" };

    // Add a fact
    await oso.insert(["has_role", alice, "member", repo]);

    // Check authorization
    const allowed = await oso.authorize(alice, "read", repo);
    console.log(`Alice can read repo: ${allowed}`); // true

  } catch (error) {
    console.error('Error:', error);
  }
}

quickStart();
```

</Tab>
<Tab title="Python">

```python
from oso_cloud import OsoCloud, Value

def quick_start():
    try:
        # Deploy a simple policy
        oso.policy("""
            actor User {}
            resource Repository {}
            
            allow(user: User, "read", repo: Repository) if
                has_role(user, "member", repo);
        """)

        # Create some entities
        alice = Value("User", "alice")
        repo = Value("Repository", "acme/widgets")

        # Add a fact
        oso.insert(("has_role", alice, "member", repo))

        # Check authorization
        allowed = oso.authorize(alice, "read", repo)
        print(f"Alice can read repo: {allowed}")  # True

    except Exception as error:
        print(f"Error: {error}")

quick_start()
```

</Tab>
<Tab title="Go">

```go
func quickStart() {
    // Deploy a simple policy
    err := osoClient.Policy(`
        actor User {}
        resource Repository {}
        
        allow(user: User, "read", repo: Repository) if
            has_role(user, "member", repo);
    `)
    if err != nil {
        log.Fatal(err)
    }

    // Create some entities
    alice := oso.NewValue("User", "alice")
    repo := oso.NewValue("Repository", "acme/widgets")

    // Add a fact
    err = osoClient.Insert(oso.NewFact("has_role", alice, "member", repo))
    if err != nil {
        log.Fatal(err)
    }

    // Check authorization
    allowed, err := osoClient.Authorize(alice, "read", repo)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Printf("Alice can read repo: %t\n", allowed) // true
}
```

</Tab>
<Tab title="Java">

```java
public void quickStart() {
    try {
        // Deploy a simple policy
        oso.policy("""
            actor User {}
            resource Repository {}
            
            allow(user: User, "read", repo: Repository) if
                has_role(user, "member", repo);
        """);

        // Create some entities
        Value alice = new Value("User", "alice");
        Value repo = new Value("Repository", "acme/widgets");

        // Add a fact
        oso.insert(new Fact("has_role", alice, new Value("member"), repo));

        // Check authorization
        boolean allowed = oso.authorize(alice, "read", repo);
        System.out.println("Alice can read repo: " + allowed); // true

    } catch (ApiException e) {
        System.err.println("Error: " + e.getMessage());
    }
}
```

</Tab>
<Tab title="Ruby">

```ruby
def quick_start
  begin
    # Deploy a simple policy
    oso.policy(<<~POLAR
      actor User {}
      resource Repository {}
      
      allow(user: User, "read", repo: Repository) if
        has_role(user, "member", repo);
    POLAR
    )

    # Create some entities
    alice = { type: "User", id: "alice" }
    repo = { type: "Repository", id: "acme/widgets" }

    # Add a fact
    oso.tell("has_role", alice, "member", repo)

    # Check authorization
    allowed = oso.authorize(alice, "read", repo)
    puts "Alice can read repo: #{allowed}" # true

  rescue => error
    puts "Error: #{error}"
  end
end

quick_start
```

</Tab>
<Tab title=".NET">

```csharp
public async Task QuickStart()
{
    try
    {
        // Deploy a simple policy
        await oso.Policy(@"
            actor User {}
            resource Repository {}
            
            allow(user: User, ""read"", repo: Repository) if
                has_role(user, ""member"", repo);
        ");

        // Create some entities
        var alice = new Value("User", "alice");
        var repo = new Value("Repository", "acme/widgets");

        // Add a fact
        await oso.Tell("has_role", new List<Value> { alice, new Value("member"), repo });

        // Check authorization
        var allowed = oso.Authorize(alice, "read", repo);
        Console.WriteLine($"Alice can read repo: {allowed}"); // True

    }
    catch (Exception error)
    {
        Console.WriteLine($"Error: {error.Message}");
    }
}
```

</Tab>
</Tabs>

## Next Steps

Now that you have the basic setup working, explore these key areas:

<CardGroup cols={2}>
  <Card
    title="Policy Management"
    icon="file"
    href="/reference/client-libraries/policies"
  >
    Learn how to deploy and manage your authorization policies
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/facts"
  >
    Understand how to store and manage authorization data
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/authorization-checks"
  >
    Master the different ways to check permissions
  </Card>
  
  <Card
    title="Local Authorization"
    icon="house"
    href="/reference/client-libraries/local-authorization"
  >
    Optimize performance with database-level filtering
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/migration-guide"
  >
    Upgrade from older SDK versions
  </Card>
</CardGroup>
