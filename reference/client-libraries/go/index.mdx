---
title: Go Client Library
description: "Complete guide to using the Oso Cloud Go client library for authorization."
sidebarTitle: "Go"
---

The Oso Cloud Go client library provides robust authorization capabilities with structured error handling, context support, and idiomatic Go patterns.

## Quick Start

```go
package main

import (
    "log"
    oso "github.com/osohq/go-oso-cloud/v2"
)

func main() {
    osoClient, err := oso.NewClient(oso.Config{
        URL:    "https://cloud.osohq.com",
        APIKey: os.Getenv("OSO_AUTH"),
    })
    if err != nil {
        log.Fatal(err)
    }
    defer osoClient.Close()

    user := oso.NewValue("User", "alice")
    resource := oso.NewValue("Repository", "acme/widgets")

    // Check authorization
    allowed, err := osoClient.Authorize(user, "read", resource)
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("Access allowed: %t\n", allowed)
}
```

## What's Included

<CardGroup cols={2}>
  <Card
    title="Installation & Setup"
    icon="download"
    href="/reference/client-libraries/go/installation"
  >
    Install via go get and configure your Go application
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/go/authorization-checks"
  >
    Core authorization methods with error handling
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/go/facts-management"
  >
    CRUD operations with structured facts
  </Card>
  
  <Card
    title="Policy Management"
    icon="file-code"
    href="/reference/client-libraries/go/policy-management"
  >
    Deploy and manage authorization policies
  </Card>
  
  <Card
    title="Local Authorization"
    icon="server"
    href="/reference/client-libraries/go/local-authorization"
  >
    GORM integration for database filtering
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/go/migration-guide"
  >
    Upgrade from v1 to v2 with new import path
  </Card>
</CardGroup>

## Key Features

### Structured Error Handling
Better error types for different failure scenarios:

```go
import "github.com/osohq/go-oso-cloud/v2/errors"

_, err := osoClient.Authorize(user, action, resource)
if err != nil {
    switch e := err.(type) {
    case *errors.PolicyNotFoundError:
        log.Println("No policy deployed")
        return false // Fail closed
    case *errors.InvalidActorError:
        log.Println("Invalid user format")
        return errors.New("authentication required")
    default:
        log.Printf("Authorization check failed: %v", e)
        return err
    }
}
```

### Context Support
All methods support Go's context package:

```go
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

allowed, err := osoClient.AuthorizeWithContext(ctx, user, action, resource)
```

## Framework Integration

### Gin
```go
func AuthorizeMiddleware(osoClient *oso.Client) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetString("user_id")
        user := oso.NewValue("User", userID)
        
        // Add authorization logic
        c.Next()
    }
}
```

### Echo
```go
func AuthorizeMiddleware(osoClient *oso.Client) echo.MiddlewareFunc {
    return func(next echo.HandlerFunc) echo.HandlerFunc {
        return func(c echo.Context) error {
            // Add authorization logic
            return next(c)
        }
    }
}
```

## Next Steps

1. **[Install the package](/reference/client-libraries/go/installation)** - Get up and running
2. **[Learn authorization](/reference/client-libraries/go/authorization-checks)** - Master core concepts
3. **[See examples](https://github.com/osohq/oso-cloud-go-examples)** - Real implementations
