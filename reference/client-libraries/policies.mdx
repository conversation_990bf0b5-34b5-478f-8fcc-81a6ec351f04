---
title: Policy Management
description: "This page covers policy deployment, metadata retrieval, and best practices for policy management."
sidebarTitle: "Policies"
---

The Policy API allows you to deploy and manage your Polar authorization policies in Oso Cloud. Oso Cloud policies are written in [Polar](/reference/polar/introduction), a declarative policy language designed for authorization. When you deploy a policy:

1. **Syntax Validation**: The policy is checked for syntax errors
2. **Test Execution**: All policy tests are run automatically  
3. **Deployment**: If tests pass, the policy becomes active
4. **Rollback**: If tests fail, the deployment is rejected

## Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

<Tabs>
<Tab title="Node.js">

```javascript
await oso.policy(policy: string): Promise<void>
```

**Example:**
```javascript
try {
  await oso.policy(`
    actor User {}
    resource Repository {
      permissions = ["read", "write", "admin"];
      roles = ["member", "maintainer", "admin"];
      
      "member" if "maintainer";
      "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
      has_role(user, "member", repo);
      
    allow(user: User, "write", repo: Repository) if
      has_role(user, "maintainer", repo);
      
    allow(user: User, "admin", repo: Repository) if
      has_role(user, "admin", repo);
      
    test "basic permissions" {
      setup {
        has_role(User{"alice"}, "member", Repository{"widgets"});
      }
      
      assert allow(User{"alice"}, "read", Repository{"widgets"});
      assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
  `);
  
  console.log("Policy deployed successfully!");
} catch (error) {
  console.error("Policy deployment failed:", error.message);
}
```

</Tab>
<Tab title="Python">

```python
oso.policy(policy: str) -> None
```

**Example:**
```python
try:
    oso.policy("""
        actor User {}
        resource Repository {
          permissions = ["read", "write", "admin"];
          roles = ["member", "maintainer", "admin"];
          
          "member" if "maintainer";
          "maintainer" if "admin";
        }
        
        allow(user: User, "read", repo: Repository) if
          has_role(user, "member", repo);
          
        allow(user: User, "write", repo: Repository) if
          has_role(user, "maintainer", repo);
          
        allow(user: User, "admin", repo: Repository) if
          has_role(user, "admin", repo);
          
        test "basic permissions" {
          setup {
            has_role(User{"alice"}, "member", Repository{"widgets"});
          }
          
          assert allow(User{"alice"}, "read", Repository{"widgets"});
          assert_not allow(User{"alice"}, "write", Repository{"widgets"});
        }
    """)
    
    print("Policy deployed successfully!")
except Exception as error:
    print(f"Policy deployment failed: {error}")
```

</Tab>
<Tab title="Go">

```go
osoClient.Policy(policy string) error
```

**Example:**
```go
policy := `
    actor User {}
    resource Repository {
      permissions = ["read", "write", "admin"];
      roles = ["member", "maintainer", "admin"];
      
      "member" if "maintainer";
      "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
      has_role(user, "member", repo);
      
    allow(user: User, "write", repo: Repository) if
      has_role(user, "maintainer", repo);
      
    allow(user: User, "admin", repo: Repository) if
      has_role(user, "admin", repo);
      
    test "basic permissions" {
      setup {
        has_role(User{"alice"}, "member", Repository{"widgets"});
      }
      
      assert allow(User{"alice"}, "read", Repository{"widgets"});
      assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
`

err := osoClient.Policy(policy)
if err != nil {
    log.Printf("Policy deployment failed: %v", err)
} else {
    fmt.Println("Policy deployed successfully!")
}
```

</Tab>
<Tab title="Java">

```java
oso.policy(policy: String): void throws ApiException
```

**Example:**
```java
try {
    oso.policy("""
        actor User {}
        resource Repository {
          permissions = ["read", "write", "admin"];
          roles = ["member", "maintainer", "admin"];
          
          "member" if "maintainer";
          "maintainer" if "admin";
        }
        
        allow(user: User, "read", repo: Repository) if
          has_role(user, "member", repo);
          
        allow(user: User, "write", repo: Repository) if
          has_role(user, "maintainer", repo);
          
        allow(user: User, "admin", repo: Repository) if
          has_role(user, "admin", repo);
          
        test "basic permissions" {
          setup {
            has_role(User{"alice"}, "member", Repository{"widgets"});
          }
          
          assert allow(User{"alice"}, "read", Repository{"widgets"});
          assert_not allow(User{"alice"}, "write", Repository{"widgets"});
        }
    """);
    
    System.out.println("Policy deployed successfully!");
} catch (ApiException e) {
    System.err.println("Policy deployment failed: " + e.getMessage());
}
```

</Tab>
<Tab title="Ruby">

```ruby
oso.policy(policy)
```

**Example:**
```ruby
begin
  oso.policy(<<~POLAR
    actor User {}
    resource Repository {
      permissions = ["read", "write", "admin"];
      roles = ["member", "maintainer", "admin"];
      
      "member" if "maintainer";
      "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
      has_role(user, "member", repo);
      
    allow(user: User, "write", repo: Repository) if
      has_role(user, "maintainer", repo);
      
    allow(user: User, "admin", repo: Repository) if
      has_role(user, "admin", repo);
      
    test "basic permissions" {
      setup {
        has_role(User{"alice"}, "member", Repository{"widgets"});
      }
      
      assert allow(User{"alice"}, "read", Repository{"widgets"});
      assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
  POLAR
  )
  
  puts "Policy deployed successfully!"
rescue => error
  puts "Policy deployment failed: #{error.message}"
end
```

</Tab>
<Tab title=".NET">

```csharp
oso.Policy(policy: string): void
```

**Example:**
```csharp
try
{
    oso.Policy(@"
        actor User {}
        resource Repository {
          permissions = [""read"", ""write"", ""admin""];
          roles = [""member"", ""maintainer"", ""admin""];
          
          ""member"" if ""maintainer"";
          ""maintainer"" if ""admin"";
        }
        
        allow(user: User, ""read"", repo: Repository) if
          has_role(user, ""member"", repo);
          
        allow(user: User, ""write"", repo: Repository) if
          has_role(user, ""maintainer"", repo);
          
        allow(user: User, ""admin"", repo: Repository) if
          has_role(user, ""admin"", repo);
          
        test ""basic permissions"" {
          setup {
            has_role(User{""alice""}, ""member"", Repository{""widgets""});
          }
          
          assert allow(User{""alice""}, ""read"", Repository{""widgets""});
          assert_not allow(User{""alice""}, ""write"", Repository{""widgets""});
        }
    ");
    
    Console.WriteLine("Policy deployed successfully!");
}
catch (Exception error)
{
    Console.WriteLine($"Policy deployment failed: {error.Message}");
}
```

</Tab>
</Tabs>

### Error Handling

Policy deployment can fail for several reasons:

- **Syntax Errors**: Invalid Polar syntax
- **Test Failures**: Policy tests don't pass
- **Type Errors**: Inconsistent type usage
- **Network Issues**: Connection problems with Oso Cloud

Always wrap policy deployment in appropriate error handling for your language.

## Get Policy Metadata

Retrieves metadata about the currently deployed policy, including version information and deployment timestamps.

<Tabs>
<Tab title="Node.js">

```javascript
oso.getPolicyMetadata(): Promise<PolicyMetadata>
```

**Example:**
```javascript
try {
  const metadata = await oso.getPolicyMetadata();
  
  console.log("Policy Version:", metadata.version);
  console.log("Deployed At:", metadata.deployedAt);
  console.log("Resource Types:", metadata.resourceTypes);
  console.log("Actor Types:", metadata.actorTypes);
} catch (error) {
  console.error("Failed to get policy metadata:", error.message);
}
```

</Tab>
<Tab title="Python">

```python
oso.get_policy_metadata() -> PolicyMetadata
```

**Example:**
```python
try:
    metadata = oso.get_policy_metadata()
    
    print(f"Policy Version: {metadata.version}")
    print(f"Deployed At: {metadata.deployed_at}")
    print(f"Resource Types: {metadata.resource_types}")
    print(f"Actor Types: {metadata.actor_types}")
except Exception as error:
    print(f"Failed to get policy metadata: {error}")
```

</Tab>
<Tab title="Go">

```go
osoClient.GetPolicyMetadata() (PolicyMetadata, error)
```

**Example:**
```go
metadata, err := osoClient.GetPolicyMetadata()
if err != nil {
    log.Printf("Failed to get policy metadata: %v", err)
    return
}

fmt.Printf("Policy Version: %s\n", metadata.Version)
fmt.Printf("Deployed At: %s\n", metadata.DeployedAt)
fmt.Printf("Resource Types: %v\n", metadata.ResourceTypes)
fmt.Printf("Actor Types: %v\n", metadata.ActorTypes)
```

</Tab>
<Tab title="Java">

```java
oso.policyMetadata(): PolicyMetadata
```

**Example:**
```java
try {
    PolicyMetadata metadata = oso.policyMetadata();
    
    System.out.println("Policy Version: " + metadata.getVersion());
    System.out.println("Deployed At: " + metadata.getDeployedAt());
    System.out.println("Resource Types: " + metadata.getResourceTypes());
    System.out.println("Actor Types: " + metadata.getActorTypes());
} catch (ApiException e) {
    System.err.println("Failed to get policy metadata: " + e.getMessage());
}
```

</Tab>
<Tab title="Ruby">

```ruby
oso.get_policy_metadata
```

**Example:**
```ruby
begin
  metadata = oso.get_policy_metadata
  
  puts "Policy Version: #{metadata.version}"
  puts "Deployed At: #{metadata.deployed_at}"
  puts "Resource Types: #{metadata.resource_types}"
  puts "Actor Types: #{metadata.actor_types}"
rescue => error
  puts "Failed to get policy metadata: #{error.message}"
end
```

</Tab>
<Tab title=".NET">

```csharp
oso.GetPolicyMetadata(): Task<PolicyMetadata>
```

**Example:**
```csharp
try
{
    var metadata = await oso.GetPolicyMetadata();
    
    Console.WriteLine($"Policy Version: {metadata.Version}");
    Console.WriteLine($"Deployed At: {metadata.DeployedAt}");
    Console.WriteLine($"Resource Types: {string.Join(", ", metadata.ResourceTypes)}");
    Console.WriteLine($"Actor Types: {string.Join(", ", metadata.ActorTypes)}");
}
catch (Exception error)
{
    Console.WriteLine($"Failed to get policy metadata: {error.Message}");
}
```

</Tab>
</Tabs>

### PolicyMetadata Object

The `PolicyMetadata` object contains:

- **`version`**: Unique identifier for the policy version
- **`deployedAt`**: Timestamp when the policy was deployed
- **`resourceTypes`**: Array of resource types defined in the policy
- **`actorTypes`**: Array of actor types defined in the policy
- **`permissions`**: Map of resource types to their defined permissions
- **`roles`**: Map of resource types to their defined roles

## Policy Development Workflow

1. **Write Tests First**: Start with test cases that define expected behavior
2. **Incremental Development**: Build policies incrementally, testing each addition
3. **Version Control**: Store policies in version control with your application code
4. **Staging Environment**: Test policies in a staging environment before production

## HTTP API Reference

These client library methods correspond to the following HTTP API endpoints:

- [Deploy Policy](/reference/api/policy/post-policy): `POST /policy`
- [Get Policy](/reference/api/policy/get-policy): `GET /policy`
- [Get Policy Metadata](/reference/api/policy/get-policy_metadata): `GET /policy/metadata`

## Next Steps

<CardGroup cols={2}>
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/facts"
  >
    Learn how to manage authorization data
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/authorization-checks"
  >
    Perform authorization decisions
  </Card>
  
  <Card
    title="Policy Patterns"
    icon="puzzle-piece"
    href="/develop/policies/patterns"
  >
    Explore common authorization patterns
  </Card>
  
  <Card
    title="Polar Language Reference"
    icon="code"
    href="/reference/polar/introduction"
  >
    Deep dive into Polar syntax and features
  </Card>
</CardGroup>
