---
title: Oso Cloud Client Libraries
description: "Welcome to the Oso Cloud client library documentation."
sidebarTitle: "Overview"
---

Choose your programming language to get started with Oso Cloud authorization:

<CardGroup cols={2}>
  <Card
    title="Node.js"
    icon="node-js"
    href="/reference/client-libraries/nodejs/"
  >
    Complete Node.js client library guide with TypeScript support and modern async/await patterns.
  </Card>
  <Card
    title="Python"
    icon="python"
    href="/reference/client-libraries/python/"
  >
    Full Python client library documentation with type hints and context manager support.
  </Card>

  <Card
    title="Go"
    icon="golang"
    href="/reference/client-libraries/go/"
  >
    Comprehensive Go client library guide with structured error handling and context support.
  </Card>

  <Card
    title="Java"
    icon="java"
    href="/reference/client-libraries/java/"
  >
    Complete Java client library documentation with builder patterns and exception handling.
  </Card>

  <Card
    title="Ruby"
    icon="gem"
    href="/reference/client-libraries/ruby/"
  >
    Full Ruby client library guide with Rails integration and idiomatic Ruby patterns.
  </Card>

  <Card
    title=".NET"
    icon="microsoft"
    href="/reference/client-libraries/dotnet/"
  >
    Comprehensive .NET client library documentation with dependency injection and async support.
  </Card>
</CardGroup>

## What's Included

Each language guide covers:

- **Installation & Setup** - Get up and running quickly
- **Authorization Checks** - Core authorization methods (`authorize`, `list`, `actions`, `buildQuery`)
- **Facts Management** - CRUD operations for authorization data (`insert`, `get`, `delete`, `batch`)
- **Policy Management** - Deploy and manage Polar policies
- **Local Authorization** - Database-level filtering for performance
- **Migration Guide** - Upgrade instructions and breaking changes
- **Best Practices** - Error handling, caching, and performance optimization

## Supported Languages

| Language | SDK Version | Key Features |
|----------|-------------|--------------|
| **Node.js** | v2.x | TypeScript support, modern async/await |
| **Python** | v2.x | Type hints, context managers |
| **Go** | v2.x | Structured errors, context support |
| **Java** | v1.x | Builder patterns, strong typing |
| **Ruby** | Stable | Rails integration, idiomatic Ruby |
| **C#/.NET** | Stable | Dependency injection, async/await |


