---
title: Oso Cloud Client Libraries
description: "Welcome to the Oso Cloud client library documentation."
sidebarTitle: "Overview"
---

<CardGroup cols={2}>
  <Card
    title="Install and Setup"
    icon="globe"
    href="/reference/client-libraries/install"
  >
    Deploy and manage your authorization policies using the Polar policy language.
  </Card>
  <Card
    title="Policy Management"
    icon="file"
    href="/reference/client-libraries/policies"
  >
    Deploy and manage your authorization policies using the Polar policy language.
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/facts"
  >
    Store and manage authorization data with full CRUD operations and wildcard patterns.
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/authorization-checks"
  >
    Perform authorization decisions with basic checks and advanced query building.
  </Card>
  
  <Card
    title="Local Authorization"
    icon="house"
    href="/reference/client-libraries/local-authorization"
  >
    Optimize performance with database-level filtering for large-scale applications.
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/migration-guide"
  >
    Upgrade from older SDK versions with language-specific migration instructions.
  </Card>
</CardGroup>

## Supported Languages

| Language | SDK Version |
|----------|-------------|
| **Node.js** | v2.x |
| **Python** | v2.x |
| **Go** | v2.x |
| **Java** | v1.x |
| **Ruby** | Stable |
| **C#/.NET** | Stable |


