---
title: Facts Management
description: "This page covers all CRUD operations for managing facts: creating, reading, updating, and deleting authorization data."
sidebarTitle: "Facts"
---

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions. Common examples include:

- **Role assignments**: `has_role(user, "admin", organization)`
- **Resource ownership**: `owns(user, document)`  
- **Group membership**: `member_of(user, team)`
- **Resource properties**: `is_public(repository)`

Facts are structured as predicates with arguments, similar to function calls. Each fact consists of:
- **Predicate name**: The relationship type (e.g., `has_role`, `owns`)
- **Arguments**: The entities involved in the relationship

## Insert Facts

Adds a single fact to the centralized authorization data store.

<Tabs>
<Tab title="Node.js">

```javascript
oso.insert(fact: [string, ...Value[]]): Promise<void>
```

**Example:**
```javascript
import { Value } from 'oso-cloud';

// Create entity values
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");
const org = new Value("Organization", "acme");

// Insert role assignment
await oso.insert(["has_role", alice, "admin", repo]);

// Insert organization membership
await oso.insert(["member_of", alice, org]);

// Insert resource property
await oso.insert(["is_public", repo]);
```

**Migration from v1:**
```javascript
// Old (v1)
await oso.tell("has_role", alice, "admin", repo);

// New (v2)
await oso.insert(["has_role", alice, "admin", repo]);
```

</Tab>
<Tab title="Python">

```python
oso.insert(fact: tuple[str, ...]) -> None
```

**Example:**
```python
from oso_cloud import Value

# Create entity values
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")
org = Value("Organization", "acme")

# Insert role assignment
oso.insert(("has_role", alice, "admin", repo))

# Insert organization membership
oso.insert(("member_of", alice, org))

# Insert resource property
oso.insert(("is_public", repo))
```

**Migration from v1:**
```python
# Old (v1)
oso.tell("has_role", alice, "admin", repo)

# New (v2)  
oso.insert(("has_role", alice, "admin", repo))
```

</Tab>
<Tab title="Go">

```go
osoClient.Insert(fact Fact) error
```

**Example:**
```go
import oso "github.com/osohq/go-oso-cloud/v2"

// Create entity values
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")
org := oso.NewValue("Organization", "acme")

// Insert role assignment
err := osoClient.Insert(oso.NewFact("has_role", alice, "admin", repo))
if err != nil {
    log.Fatal(err)
}

// Insert organization membership
err = osoClient.Insert(oso.NewFact("member_of", alice, org))
if err != nil {
    log.Fatal(err)
}

// Insert resource property
err = osoClient.Insert(oso.NewFact("is_public", repo))
if err != nil {
    log.Fatal(err)
}
```

**Migration from v1:**
```go
// Old (v1)
err := osoClient.Tell("has_role", alice, "admin", repo)

// New (v2)
err := osoClient.Insert(oso.NewFact("has_role", alice, "admin", repo))
```

</Tab>
<Tab title="Java">

```java
oso.insert(fact: Fact): void throws ApiException
```

**Example:**
```java
import com.osohq.oso_cloud.*;

// Create entity values
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");
Value org = new Value("Organization", "acme");

// Insert role assignment
oso.insert(new Fact("has_role", alice, new Value("admin"), repo));

// Insert organization membership
oso.insert(new Fact("member_of", alice, org));

// Insert resource property
oso.insert(new Fact("is_public", repo));
```

**Migration from v0:**
```java
// Old (v0)
oso.tell("has_role", alice, new Value("admin"), repo);

// New (v1)
oso.insert(new Fact("has_role", alice, new Value("admin"), repo));
```

</Tab>
<Tab title="Ruby">

```ruby
oso.tell(name, *args)
```

**Example:**
```ruby
# Create entity values
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }
org = { type: "Organization", id: "acme" }

# Insert role assignment
oso.tell("has_role", alice, "admin", repo)

# Insert organization membership
oso.tell("member_of", alice, org)

# Insert resource property
oso.tell("is_public", repo)
```

**No migration needed** - Ruby SDK has stable API.

</Tab>
<Tab title=".NET">

```csharp
oso.Tell(name: string, args: List<Value>): Task<void>
```

**Example:**
```csharp
using OsoCloud;

// Create entity values
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");
var org = new Value("Organization", "acme");

// Insert role assignment
await oso.Tell("has_role", new List<Value> { alice, new Value("admin"), repo });

// Insert organization membership
await oso.Tell("member_of", new List<Value> { alice, org });

// Insert resource property
await oso.Tell("is_public", new List<Value> { repo });
```

**No migration needed** - .NET SDK has stable API.

</Tab>
</Tabs>

## Delete Facts

Deletes facts from the authorization data store. Supports wildcard patterns for bulk deletion.

<Tabs>
<Tab title="Node.js">

```javascript
oso.delete(pattern: [string, ...Value[]]): Promise<void>
```

**Example:**
```javascript
// Delete specific fact
await oso.delete(["has_role", alice, "admin", repo]);

// Delete all roles for a user (wildcard with null)
await oso.delete(["has_role", alice, null, null]);

// Delete all admin roles for a specific resource
await oso.delete(["has_role", null, "admin", repo]);
```

**Wildcard Support:**
- `null` - matches any value
- Use `null` for flexible fact deletion patterns

</Tab>
<Tab title="Python">

```python
oso.delete(pattern: tuple[str, ...]) -> None
```

**Example:**
```python
from oso_cloud import ValueOfType

# Delete specific fact
oso.delete(("has_role", alice, "admin", repo))

# Delete all roles for a user (wildcard with None)
oso.delete(("has_role", alice, None, None))

# Delete all admin roles for repositories (type constraint)
oso.delete(("has_role", None, "admin", ValueOfType("Repository")))
```

**Wildcard Support:**
- `None` - matches any value
- `ValueOfType("TypeName")` - matches any value of specific type

</Tab>
<Tab title="Go">

```go
osoClient.Delete(pattern FactPattern) error
```

**Example:**
```go
// Delete specific fact
err := osoClient.Delete(oso.NewFactPattern("has_role", alice, "admin", repo))

// Delete all roles for a user (wildcard with nil)
err = osoClient.Delete(oso.NewFactPattern("has_role", alice, nil, nil))

// Delete all admin roles for repositories (type constraint)
err = osoClient.Delete(oso.NewFactPattern("has_role", nil, "admin", oso.NewValueOfType("Repository")))
```

**Wildcard Support:**
- `nil` - matches any value
- `oso.NewValueOfType("TypeName")` - matches any value of specific type

</Tab>
<Tab title="Java">

```java
oso.delete(pattern: FactPattern): void throws ApiException
```

**Example:**
```java
import com.osohq.oso_cloud.ValuePattern;

// Delete specific fact
oso.delete(new FactPattern("has_role", alice, new ValuePattern.Value("admin"), repo));

// Delete all roles for a user (wildcard)
oso.delete(new FactPattern("has_role", alice, ValuePattern.ANY, ValuePattern.ANY));

// Delete all admin roles for repositories (type constraint)
oso.delete(new FactPattern("has_role", ValuePattern.ANY, new ValuePattern.Value("admin"), 
                          new ValuePattern.ValueOfType("Repository")));
```

**Wildcard Support:**
- `ValuePattern.ANY` - matches any value
- `ValuePattern.ValueOfType("TypeName")` - matches any value of specific type

</Tab>
<Tab title="Ruby">

```ruby
oso.delete(name, *args)
```

**Example:**
```ruby
# Delete specific fact
oso.delete("has_role", alice, "admin", repo)

# Delete all roles for a user (wildcard with nil)
oso.delete("has_role", alice, nil, nil)

# Delete all admin roles for a specific resource
oso.delete("has_role", nil, "admin", repo)
```

**Wildcard Support:**
- `nil` - matches any value

</Tab>
<Tab title=".NET">

```csharp
oso.Delete(name: string, args: List<Value>): Task<void>
```

**Example:**
```csharp
// Delete specific fact
await oso.Delete("has_role", new List<Value> { alice, new Value("admin"), repo });

// Delete all roles for a user (wildcard with null)
await oso.Delete("has_role", new List<Value> { alice, null, null });

// Delete all admin roles for a specific resource
await oso.Delete("has_role", new List<Value> { null, new Value("admin"), repo });
```

**Wildcard Support:**
- `null` - matches any value

</Tab>
</Tabs>

## Get Facts

Retrieves facts from the authorization data store. Supports wildcard patterns for querying multiple facts.

<Tabs>
<Tab title="Node.js">

```javascript
oso.get(pattern: [string, ...Value[]]): Promise<Fact[]>
```

**Example:**
```javascript
// Get specific fact
const exactFacts = await oso.get(["has_role", alice, "admin", repo]);

// Get all roles for a user
const userRoles = await oso.get(["has_role", alice, null, null]);

// Get all users with admin role on specific resource
const adminUsers = await oso.get(["has_role", null, "admin", repo]);

// Process results
userRoles.forEach(fact => {
  console.log(`Role: ${fact.args[1]}, Resource: ${fact.args[2].id}`);
});
```

</Tab>
<Tab title="Python">

```python
oso.get(pattern: tuple[str, ...]) -> List[Fact]
```

**Example:**
```python
from oso_cloud import ValueOfType

# Get specific fact
exact_facts = oso.get(("has_role", alice, "admin", repo))

# Get all roles for a user
user_roles = oso.get(("has_role", alice, None, None))

# Get all admin roles for repositories
admin_roles = oso.get(("has_role", None, "admin", ValueOfType("Repository")))

# Process results
for fact in user_roles:
    print(f"Role: {fact.args[1]}, Resource: {fact.args[2].id}")
```

</Tab>
<Tab title="Go">

```go
osoClient.Get(pattern FactPattern) ([]Fact, error)
```

**Example:**
```go
// Get specific fact
exactFacts, err := osoClient.Get(oso.NewFactPattern("has_role", alice, "admin", repo))

// Get all roles for a user
userRoles, err := osoClient.Get(oso.NewFactPattern("has_role", alice, nil, nil))

// Get all admin roles for repositories
adminRoles, err := osoClient.Get(oso.NewFactPattern("has_role", nil, "admin", oso.NewValueOfType("Repository")))

// Process results
for _, fact := range userRoles {
    fmt.Printf("Role: %s, Resource: %s\n", fact.Args[1], fact.Args[2])
}
```

</Tab>
<Tab title="Java">

```java
oso.get(pattern: FactPattern): Fact[] throws ApiException
```

**Example:**
```java
import com.osohq.oso_cloud.ValuePattern;

// Get specific fact
Fact[] exactFacts = oso.get(new FactPattern("has_role", alice, new ValuePattern.Value("admin"), repo));

// Get all roles for a user
Fact[] userRoles = oso.get(new FactPattern("has_role", alice, ValuePattern.ANY, ValuePattern.ANY));

// Get all admin roles for repositories
Fact[] adminRoles = oso.get(new FactPattern("has_role", ValuePattern.ANY, 
                                           new ValuePattern.Value("admin"), 
                                           new ValuePattern.ValueOfType("Repository")));

// Process results
for (Fact fact : userRoles) {
    System.out.println("Role: " + fact.getArgs().get(1) + ", Resource: " + fact.getArgs().get(2));
}
```

</Tab>
<Tab title="Ruby">

```ruby
oso.get(name, *args)
```

**Example:**
```ruby
# Get specific fact
exact_facts = oso.get("has_role", alice, "admin", repo)

# Get all roles for a user
user_roles = oso.get("has_role", alice, nil, nil)

# Get all admin roles for a specific resource
admin_roles = oso.get("has_role", nil, "admin", repo)

# Process results
user_roles.each do |fact|
  puts "Role: #{fact.args[1]}, Resource: #{fact.args[2]['id']}"
end
```

</Tab>
<Tab title=".NET">

```csharp
oso.Get(name: string, args: List<Value>): Task<Fact[]>
```

**Example:**
```csharp
// Get specific fact
var exactFacts = await oso.Get("has_role", new List<Value> { alice, new Value("admin"), repo });

// Get all roles for a user
var userRoles = await oso.Get("has_role", new List<Value> { alice, null, null });

// Get all admin roles for a specific resource
var adminRoles = await oso.Get("has_role", new List<Value> { null, new Value("admin"), repo });

// Process results
foreach (var fact in userRoles)
{
    Console.WriteLine($"Role: {fact.Args[1]}, Resource: {fact.Args[2]}");
}
```

</Tab>
</Tabs>

## Batch Operations

Perform multiple fact operations in a single transaction for better performance and consistency.

<Tabs>
<Tab title="Node.js">

```javascript
oso.batch(callback: (tx: Transaction) => void): Promise<void>
```

**Example:**
```javascript
await oso.batch((tx) => {
  // Remove old role
  tx.delete(["has_role", alice, "member", repo]);
  
  // Add new role
  tx.insert(["has_role", alice, "admin", repo]);
  
  // Add additional permissions
  tx.insert(["can_invite", alice, repo]);
});
```

**Migration from v1:**
```javascript
// Old (v1)
await oso.bulk([["has_role", alice, null, null]], [["has_role", alice, "admin", repo]]);

// New (v2)
await oso.batch((tx) => {
  tx.delete(["has_role", alice, null, null]);
  tx.insert(["has_role", alice, "admin", repo]);
});
```

</Tab>
<Tab title="Python">

```python
oso.batch(callback: Callable[[Transaction], None]) -> None
```

**Example:**
```python
def update_user_role():
    # Remove old role
    oso.delete(("has_role", alice, "member", repo))
    
    # Add new role  
    oso.insert(("has_role", alice, "admin", repo))
    
    # Add additional permissions
    oso.insert(("can_invite", alice, repo))

# Using context manager
with oso.batch() as tx:
    tx.delete(("has_role", alice, "member", repo))
    tx.insert(("has_role", alice, "admin", repo))
    tx.insert(("can_invite", alice, repo))
```

</Tab>
<Tab title="Go">

```go
osoClient.Batch(operations []BatchOperation) error
```

**Example:**
```go
operations := []oso.BatchOperation{
    oso.NewDeleteOperation(oso.NewFactPattern("has_role", alice, "member", repo)),
    oso.NewInsertOperation(oso.NewFact("has_role", alice, "admin", repo)),
    oso.NewInsertOperation(oso.NewFact("can_invite", alice, repo)),
}

err := osoClient.Batch(operations)
if err != nil {
    log.Fatal(err)
}
```

</Tab>
<Tab title="Java">

```java
oso.batch(operations: List<BatchOperation>): void throws ApiException
```

**Example:**
```java
List<BatchOperation> operations = Arrays.asList(
    BatchOperation.delete(new FactPattern("has_role", alice, new ValuePattern.Value("member"), repo)),
    BatchOperation.insert(new Fact("has_role", alice, new Value("admin"), repo)),
    BatchOperation.insert(new Fact("can_invite", alice, repo))
);

oso.batch(operations);
```

</Tab>
<Tab title="Ruby">

```ruby
# Ruby SDK handles batching automatically for performance
# Multiple operations in sequence are optimized internally

oso.delete("has_role", alice, "member", repo)
oso.tell("has_role", alice, "admin", repo)
oso.tell("can_invite", alice, repo)
```

</Tab>
<Tab title=".NET">

```csharp
// .NET SDK handles batching automatically for performance
// Multiple operations in sequence are optimized internally

await oso.Delete("has_role", new List<Value> { alice, new Value("member"), repo });
await oso.Tell("has_role", new List<Value> { alice, new Value("admin"), repo });
await oso.Tell("can_invite", new List<Value> { alice, repo });
```

</Tab>
</Tabs>

## Wildcard Patterns

Wildcard patterns allow you to query and delete multiple facts efficiently:

### Pattern Matching

| Pattern | Matches |
|---------|---------|
| `null`/`nil`/`None` | Any value in that position |
| `ValueOfType("Type")` | Any value of the specified type |
| Specific value | Exact match only |

### Common Patterns

```javascript
// Get all roles for a user
oso.get(["has_role", user, null, null])

// Get all users with a specific role
oso.get(["has_role", null, "admin", null])

// Get all facts about a resource
oso.get([null, null, null, resource])

// Delete all permissions for a user
oso.delete(["has_permission", user, null, null])
```

## Best Practices

### Fact Design

1. **Consistent Naming**: Use consistent predicate names across your application
2. **Meaningful Predicates**: Choose descriptive names like `has_role`, `owns`, `can_access`
3. **Argument Order**: Establish consistent argument ordering (e.g., subject, role, object)

### Performance Optimization

1. **Batch Operations**: Use batch operations for multiple related changes
2. **Efficient Queries**: Use specific patterns rather than overly broad wildcards
3. **Fact Lifecycle**: Clean up unused facts to maintain performance

### Data Consistency

1. **Transactional Updates**: Use batch operations for related changes
2. **Fact Validation**: Validate fact data before insertion
3. **Cleanup Procedures**: Implement procedures to clean up orphaned facts

### Error Handling

```javascript
try {
  await oso.insert(["has_role", user, role, resource]);
} catch (error) {
  if (error.code === 'DUPLICATE_FACT') {
    console.log('Fact already exists');
  } else if (error.code === 'INVALID_VALUE') {
    console.log('Invalid entity value');
  } else {
    console.error('Unexpected error:', error);
  }
}
```

## HTTP API Reference

These client library methods correspond to the following HTTP API endpoints:

- [Insert Facts](/reference/api/centralized-authorization-data/post-facts): `POST /facts`
- [Delete Facts](/reference/api/centralized-authorization-data/delete-facts): `DELETE /facts`
- [Get Facts](/reference/api/centralized-authorization-data/get-facts): `GET /facts`
- [Batch Operations](/reference/api/centralized-authorization-data/post-batch): `POST /batch`
