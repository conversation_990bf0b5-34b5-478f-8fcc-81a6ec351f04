---
title: Node.js Client Library
description: "Complete guide to using the Oso Cloud Node.js client library for authorization."
sidebarTitle: "Node.js"
---

## Installation

Install the Oso Cloud Node.js client:

```bash
npm install oso-cloud
```

**Requirements:**
- Node.js 16 or later
- TypeScript support included

## Basic Setup

Initialize the Oso Cloud client with your API key:

```javascript
import { Oso } from 'oso-cloud';

const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH
});
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```javascript
const oso = new Oso({
  url: "https://cloud.osohq.com",
  apiKey: process.env.OSO_AUTH,
  timeout: 30000,  // Request timeout in milliseconds
  retries: 3       // Number of retry attempts
});
```

## Authorization Checks

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

### Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```javascript
oso.authorize(actor: Value, action: string, resource: Value, contextFacts?: Fact[]): Promise<boolean>
```

**Basic Example:**
```javascript
import { Value } from 'oso-cloud';

const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
const canRead = await oso.authorize(alice, "read", repo);
console.log(`Alice can read repo: ${canRead}`);

// Check if Alice can write to the repository
const canWrite = await oso.authorize(alice, "write", repo);
console.log(`Alice can write to repo: ${canWrite}`);
```

**With Context Facts:**
```javascript
// Check authorization with temporary context
const canReadPublic = await oso.authorize(
  alice, 
  "read", 
  repo, 
  [["is_public", repo]]
);
```

### List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```javascript
oso.list(actor: Value, action: string, resourceType: string, contextFacts?: Fact[]): Promise<string[]>
```

**Example:**
```javascript
const alice = new Value("User", "alice");

// Get all repositories Alice can read
const readableRepos = await oso.list(alice, "read", "Repository");
console.log("Readable repositories:", readableRepos);

// Get all organizations Alice can admin
const adminOrgs = await oso.list(alice, "admin", "Organization");
console.log("Admin organizations:", adminOrgs);
```

**With Context Facts:**
```javascript
// List with additional context
const publicRepos = await oso.list(
  alice, 
  "read", 
  "Repository",
  [["user_preference", alice, "show_public", true]]
);
```

### List Actions

Fetches all actions that an actor can perform on a specific resource.

```javascript
oso.actions(actor: Value, resource: Value, contextFacts?: Fact[]): Promise<string[]>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
const allowedActions = await oso.actions(alice, repo);
console.log("Allowed actions:", allowedActions);
// Output: ["read", "write"] or ["read", "write", "admin"]
```

### Advanced Querying

For complex authorization queries, use the Query Builder API to construct sophisticated queries with multiple conditions.

```javascript
oso.buildQuery(predicate: [string, ...Value[]]): QueryBuilder
```

**Basic Query:**
```javascript
// Find all users who can admin a specific repository
const adminUsers = await oso.buildQuery(["allow", oso.var("user"), "admin", repo])
  .evaluate(oso.var("user"));

console.log("Admin users:", adminUsers);
```

**Complex Query with Conditions:**
```javascript
// Find all repositories in the "acme" organization that Alice can read
const acmeOrg = new Value("Organization", "acme");
const readableAcmeRepos = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .and(["parent", oso.var("repo"), acmeOrg])
  .evaluate(oso.var("repo"));

console.log("Readable acme repositories:", readableAcmeRepos);
```

**Query with Context Facts:**
```javascript
// Query with temporary context
const contextQuery = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .withContextFacts([["is_public", oso.var("repo")]])
  .evaluate(oso.var("repo"));
```

**Migration from v1:**
```javascript
// Old (v1)
const results = await oso.query("allow", alice, "read", oso.var("repo"));

// New (v2)
const results = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .evaluate(oso.var("repo"));
```

## Facts Management

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

### Insert Facts

Adds a single fact to the centralized authorization data store.

```javascript
oso.insert(fact: [string, ...Value[]]): Promise<void>
```

**Example:**
```javascript
import { Value } from 'oso-cloud';

// Create entity values
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");
const org = new Value("Organization", "acme");

// Insert role assignment
await oso.insert(["has_role", alice, "admin", repo]);

// Insert organization membership
await oso.insert(["member_of", alice, org]);

// Insert resource property
await oso.insert(["is_public", repo]);
```

**Migration from v1:**
```javascript
// Old (v1)
await oso.tell("has_role", alice, "admin", repo);

// New (v2)
await oso.insert(["has_role", alice, "admin", repo]);
```

### Batch Operations

Perform multiple fact operations atomically using transactions.

```javascript
oso.batch(callback: (tx: Transaction) => void): Promise<void>
```

**Example:**
```javascript
// Atomic batch operation
await oso.batch((tx) => {
  // Remove all existing roles for Alice
  tx.delete(["has_role", alice, null, null]);
  
  // Add new role assignments
  tx.insert(["has_role", alice, "admin", repo1]);
  tx.insert(["has_role", alice, "member", repo2]);
  tx.insert(["member_of", alice, org]);
});
```

### Get Facts

Retrieve facts from the authorization data store using pattern matching.

```javascript
oso.get(pattern: [string, ...Value[]]): Promise<Fact[]>
```

**Example:**
```javascript
// Get all roles for Alice
const aliceRoles = await oso.get(["has_role", alice, null, null]);
console.log("Alice's roles:", aliceRoles);

// Get all members of an organization
const orgMembers = await oso.get(["member_of", null, org]);
console.log("Organization members:", orgMembers);

// Get specific fact
const isPublic = await oso.get(["is_public", repo]);
console.log("Repository is public:", isPublic.length > 0);
```

### Delete Facts

Remove facts from the authorization data store.

```javascript
oso.delete(pattern: [string, ...Value[]]): Promise<void>
```

**Example:**
```javascript
// Delete specific role assignment
await oso.delete(["has_role", alice, "admin", repo]);

// Delete all roles for Alice on any repository
await oso.delete(["has_role", alice, null, null]);

// Delete all facts about a repository
await oso.delete([null, null, repo]);
```

## Policy Management

Deploy and manage your Polar authorization policies in Oso Cloud.

### Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```javascript
await oso.policy(policy: string): Promise<void>
```

**Example:**
```javascript
try {
  await oso.policy(`
    actor User {}
    resource Repository {
      permissions = ["read", "write", "admin"];
      roles = ["member", "maintainer", "admin"];
      
      "member" if "maintainer";
      "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
      has_role(user, "member", repo);
      
    allow(user: User, "write", repo: Repository) if
      has_role(user, "maintainer", repo);
      
    allow(user: User, "admin", repo: Repository) if
      has_role(user, "admin", repo);
      
    test "basic permissions" {
      setup {
        has_role(User{"alice"}, "member", Repository{"widgets"});
      }
      
      assert allow(User{"alice"}, "read", Repository{"widgets"});
      assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
  `);
  
  console.log("Policy deployed successfully!");
} catch (error) {
  console.error("Policy deployment failed:", error.message);
}
```

### Get Policy

Retrieve the currently deployed policy.

```javascript
oso.getPolicy(): Promise<string>
```

**Example:**
```javascript
const currentPolicy = await oso.getPolicy();
console.log("Current policy:", currentPolicy);
```

### Policy Stats

Get statistics about your deployed policy.

```javascript
oso.stats(): Promise<PolicyStats>
```

**Example:**
```javascript
const stats = await oso.stats();
console.log("Policy statistics:", {
  numRules: stats.num_rules,
  numTypes: stats.num_types,
  numResources: stats.num_resources
});
```

## Local Authorization

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

### List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```javascript
oso.listLocal(actor: Value, action: string, resourceType: string, column: string): Promise<string>
```

**Basic Example:**
```javascript
import { Value } from 'oso-cloud';

const alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
const sqlFilter = await oso.listLocal(alice, "read", "Repository", "id");
console.log("SQL Filter:", sqlFilter);
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Kysely):**
```javascript
import { Kysely } from 'kysely';

async function getReadableRepositories(userId: string) {
  const user = new Value("User", userId);
  const filter = await oso.listLocal(user, "read", "Repository", "id");
  
  // Use the filter in your database query
  const repositories = await db
    .selectFrom('repositories')
    .selectAll()
    .where(sql`${sql.raw(filter)}`)
    .execute();
    
  return repositories;
}
```

**Advanced Usage:**
```javascript
// Get repositories Alice can admin with additional filtering
const adminFilter = await oso.listLocal(alice, "admin", "Repository", "id");
const repositories = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(adminFilter)}`)
  .where('active', '=', true)
  .where('created_at', '>', new Date('2023-01-01'))
  .execute();
```

### Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```javascript
oso.authorizeLocal(actor: Value, action: string, resource: Value, column: string): Promise<string>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get SQL condition for checking if Alice can read the repository
const condition = await oso.authorizeLocal(alice, "read", repo, "id");
console.log("SQL Condition:", condition);
// Output: "id = 'acme/widgets'"

// Use in a database query
const repository = await db
  .selectFrom('repositories')
  .selectAll()
  .where(sql`${sql.raw(condition)}`)
  .executeTakeFirst();
```

## Migration Guide

### Breaking Changes (v1 → v2)

#### Node.js Version Requirement
Upgrade your Node.js runtime to version 16 or later.

```json
{
  "engines": {
    "node": ">=16.0.0"
  }
}
```

#### Facts API Changes
All fact operations now use array-wrapped arguments instead of individual parameters.

```javascript
// New insert() method with array wrapping
await oso.insert(["has_role", user, "admin", repo]);

// New delete() method with array wrapping
await oso.delete(["has_role", user, "admin", repo]);

// New get() method with array wrapping
const facts = await oso.get(["has_role", user, null, null]);
```

#### Batch Operations
The `bulk()` method has been replaced with a transaction-based `batch()` API.

```javascript
await oso.batch((tx) => {
  tx.delete(["has_role", user, null, null]);
  tx.insert(["has_role", user, "admin", repo]);
});
```

#### Query API
The `query()` method has been replaced with `buildQuery().evaluate()`.

```javascript
const results = await oso.buildQuery(["has_role", user, "admin", repo])
  .evaluate();
```

### New Features

#### TypeScript Type Generation
Generate TypeScript types from your Polar policies:

```bash
# Install CLI
npm install -g oso-cloud-cli

# Generate types
oso-cloud generate-types --output ./src/types/oso-types.ts
```

```typescript
import { User, Repository } from './types/oso-types';

const user: User = { type: "User", id: "alice" };
const repo: Repository = { type: "Repository", id: "acme/widgets" };

// TypeScript will enforce correct types
const allowed = await oso.authorize(user, "read", repo);
```

#### Enhanced QueryBuilder
Complex queries with fluent interface and context facts:

```javascript
const users = await oso.buildQuery(["allow", oso.var("user"), "admin", oso.var("repo")])
  .and(["parent", oso.var("repo"), organization])
  .withContextFacts([["is_public", oso.var("repo")]])
  .evaluate(oso.var("user"));
```

## Error Handling

```javascript
try {
  const allowed = await oso.authorize(user, action, resource);
  return allowed;
} catch (error) {
  if (error.code === 'POLICY_NOT_FOUND') {
    console.log('No policy deployed');
    return false; // Fail closed
  } else if (error.code === 'INVALID_ACTOR') {
    console.log('Invalid user format');
    throw new Error('Authentication required');
  } else {
    console.error('Authorization check failed:', error);
    throw error; // Re-throw unexpected errors
  }
}
```

## Performance Considerations

### Caching
- **Authorization results**: Cache frequently-checked permissions
- **Resource lists**: Cache resource lists for common actors
- **Context facts**: Avoid expensive context fact computation

### Batch Operations
```javascript
// Instead of multiple individual checks
const canRead = await oso.authorize(alice, "read", repo1);
const canWrite = await oso.authorize(alice, "write", repo1);
const canAdmin = await oso.authorize(alice, "admin", repo1);

// Use actions() for multiple permission checks on same resource
const allowedActions = await oso.actions(alice, repo1);
const canRead = allowedActions.includes("read");
const canWrite = allowedActions.includes("write");
const canAdmin = allowedActions.includes("admin");
```

## Next Steps

- [Policy Patterns](/develop/policies/patterns) - Learn common authorization patterns
- [Performance Guide](/develop/troubleshooting/query-performance) - Optimize authorization performance
- [HTTP API Reference](/reference/api/check-api) - Direct API access
