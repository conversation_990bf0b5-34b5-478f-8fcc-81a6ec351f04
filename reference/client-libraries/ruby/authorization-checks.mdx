---
title: Authorization Checks
description: "Ruby client library authorization checks documentation."
sidebarTitle: "Authorization Checks"
---

# Authorization Checks

Ruby-specific implementation for authorization checks.

## Coming Soon

This section is being developed. Please refer to the main documentation for now.

## Next Steps

- **[Installation](/reference/client-libraries/ruby/installation)** - Get started with Ruby
- **[Authorization checks](/reference/client-libraries/ruby/authorization-checks)** - Core authorization methods
