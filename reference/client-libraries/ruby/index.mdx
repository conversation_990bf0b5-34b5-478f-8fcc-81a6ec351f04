---
title: Ruby Client Library
description: "Complete guide to using the Oso Cloud Ruby client library for authorization."
sidebarTitle: "Ruby"
---

The Oso Cloud Ruby client library provides idiomatic Ruby authorization capabilities with Rails integration, simple data structures, and Ruby conventions.

## Quick Start

```ruby
require 'oso-cloud'

oso = OsoCloud::Client.new(
  url: "https://cloud.osohq.com",
  api_key: ENV["OSO_AUTH"]
)

user = { type: "User", id: "alice" }
resource = { type: "Repository", id: "acme/widgets" }

# Check authorization
allowed = oso.authorize(user, "read", resource)
puts "Access allowed: #{allowed}"
```

## What's Included

<CardGroup cols={2}>
  <Card
    title="Installation & Setup"
    icon="download"
    href="/reference/client-libraries/ruby/installation"
  >
    Install via gem and configure your Ruby application
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/ruby/authorization-checks"
  >
    Core authorization with Ruby idioms
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/ruby/facts-management"
  >
    CRUD operations with array-based facts
  </Card>
  
  <Card
    title="Policy Management"
    icon="file-code"
    href="/reference/client-libraries/ruby/policy-management"
  >
    Deploy and manage policies
  </Card>
  
  <Card
    title="Local Authorization"
    icon="server"
    href="/reference/client-libraries/ruby/local-authorization"
  >
    ActiveRecord integration for database filtering
  </Card>
  
  <Card
    title="Rails Integration"
    icon="gem"
    href="/reference/client-libraries/ruby/rails-integration"
  >
    Complete Rails integration guide
  </Card>
</CardGroup>

## Key Features

### Stable API
Ruby client library has a stable API with no breaking changes:

- **Consistent Interface**: All methods maintain backward compatibility
- **Simple Data Structures**: Uses native Ruby hashes and arrays
- **Idiomatic Ruby**: Follows Ruby conventions and patterns

### Rails Integration
Native Rails integration with controllers and models:

```ruby
# app/controllers/application_controller.rb
class ApplicationController < ActionController::Base
  private
  
  def authorize_action!(action, resource)
    user = { type: "User", id: current_user.id }
    resource_obj = { type: resource.class.name, id: resource.id }
    
    unless $oso.authorize(user, action, resource_obj)
      raise ActionController::Forbidden, "Not authorized"
    end
  end
end

# app/models/repository.rb
class Repository < ApplicationRecord
  def self.readable_by(user)
    filter = $oso.list_local(
      { type: "User", id: user.id }, 
      "read", 
      "Repository", 
      "id"
    )
    
    where(filter)
  end
end
```

## Framework Integration

### Sinatra
```ruby
require 'sinatra'
require 'oso-cloud'

configure do
  set :oso, OsoCloud::Client.new(
    url: "https://cloud.osohq.com",
    api_key: ENV["OSO_AUTH"]
  )
end

before do
  if session[:user_id]
    @current_user = { type: "User", id: session[:user_id] }
  end
end

get '/repositories/:id' do
  repository = { type: "Repository", id: params[:id] }
  
  unless settings.oso.authorize(@current_user, "read", repository)
    halt 403, "Access denied"
  end
  
  # Return repository data
end
```

### Hanami
```ruby
# config/initializers/oso.rb
Hanami.app.register(:oso) do
  OsoCloud::Client.new(
    url: "https://cloud.osohq.com",
    api_key: ENV["OSO_AUTH"]
  )
end

# app/actions/repositories/show.rb
module Repositories
  class Show < Action
    include Deps[:oso]
    
    def handle(request, response)
      user = { type: "User", id: request.session[:user_id] }
      repository = { type: "Repository", id: request.params[:id] }
      
      unless oso.authorize(user, "read", repository)
        response.status = 403
        response.body = "Access denied"
        return
      end
      
      # Handle request
    end
  end
end
```

## Next Steps

1. **[Install the gem](/reference/client-libraries/ruby/installation)** - Get started quickly
2. **[Learn authorization](/reference/client-libraries/ruby/authorization-checks)** - Master core concepts
3. **[Rails integration](/reference/client-libraries/ruby/rails-integration)** - Complete Rails guide
4. **[See examples](https://github.com/osohq/oso-cloud-ruby-examples)** - Real implementations
