---
title: Java Client Library
description: "Complete guide to using the Oso Cloud Java client library for authorization."
sidebarTitle: "Java"
---

The Oso Cloud Java client library provides enterprise-grade authorization capabilities with builder patterns, strong typing, and comprehensive exception handling.

## Quick Start

```java
import com.osohq.oso_cloud.*;

public class Main {
    public static void main(String[] args) {
        OsoCloud oso = new OsoCloud.Builder()
            .url("https://cloud.osohq.com")
            .apiKey(System.getenv("OSO_AUTH"))
            .build();

        Value user = new Value("User", "alice");
        Value resource = new Value("Repository", "acme/widgets");

        // Check authorization
        boolean allowed = oso.authorize(user, "read", resource);
        System.out.println("Access allowed: " + allowed);
    }
}
```

## What's Included

<CardGroup cols={2}>
  <Card
    title="Installation & Setup"
    icon="download"
    href="/reference/client-libraries/java/installation"
  >
    Add Maven/Gradle dependency and configure
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/java/authorization-checks"
  >
    Core authorization with builder patterns
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/java/facts-management"
  >
    CRUD operations with batch support
  </Card>
  
  <Card
    title="Policy Management"
    icon="file-code"
    href="/reference/client-libraries/java/policy-management"
  >
    Deploy and manage policies
  </Card>
  
  <Card
    title="Local Authorization"
    icon="server"
    href="/reference/client-libraries/java/local-authorization"
  >
    JDBC integration for database filtering
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/java/migration-guide"
  >
    Upgrade from v0 to v1 with new features
  </Card>
</CardGroup>

## Key Features

### Builder Pattern
Fluent configuration with builder pattern:

```java
OsoCloud oso = new OsoCloud.Builder()
    .url("https://cloud.osohq.com")
    .apiKey(System.getenv("OSO_AUTH"))
    .timeout(30000)
    .retries(3)
    .enableLogging(true)
    .build();
```

### Exception Handling
Specific exception types for different error scenarios:

```java
import com.osohq.oso_cloud.exceptions.*;

try {
    boolean allowed = oso.authorize(user, action, resource);
    return allowed;
} catch (PolicyNotFoundException e) {
    System.out.println("No policy deployed");
    return false; // Fail closed
} catch (InvalidActorException e) {
    System.out.println("Invalid user format");
    throw new IllegalArgumentException("Authentication required");
} catch (ApiException e) {
    System.err.println("Authorization check failed: " + e.getMessage());
    throw e;
}
```

## Framework Integration

### Spring Boot
```java
@Configuration
public class OsoConfig {
    
    @Bean
    public OsoCloud osoCloud(@Value("${oso.api-key}") String apiKey) {
        return new OsoCloud.Builder()
            .url("https://cloud.osohq.com")
            .apiKey(apiKey)
            .build();
    }
}

@RestController
public class RepositoryController {
    
    @Autowired
    private OsoCloud oso;
    
    @GetMapping("/repositories/{id}")
    public ResponseEntity<Repository> getRepository(@PathVariable String id, Principal principal) {
        Value user = new Value("User", principal.getName());
        Value repository = new Value("Repository", id);
        
        if (!oso.authorize(user, "read", repository)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        
        // Return repository
        return ResponseEntity.ok(repositoryService.findById(id));
    }
}
```

## Next Steps

1. **[Install the package](/reference/client-libraries/java/installation)** - Add to your project
2. **[Learn authorization](/reference/client-libraries/java/authorization-checks)** - Master core concepts
3. **[See examples](https://github.com/osohq/oso-cloud-java-examples)** - Real implementations
