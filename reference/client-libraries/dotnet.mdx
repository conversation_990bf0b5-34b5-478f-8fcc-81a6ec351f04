---
title: .NET Client Library
description: "Complete guide to using the Oso Cloud .NET client library for authorization."
sidebarTitle: ".NET"
---

## Installation

Install the Oso Cloud .NET package:

```bash
dotnet add package OsoCloud
```

**Requirements:**
- .NET 6.0 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```csharp
using OsoCloud;

var oso = new OsoClient(new OsoConfig
{
    Url = "https://cloud.osohq.com",
    ApiKey = Environment.GetEnvironmentVariable("OSO_AUTH")
});
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```csharp
var oso = new OsoClient(new OsoConfig
{
    Url = "https://cloud.osohq.com",
    ApiKey = Environment.GetEnvironmentVariable("OSO_AUTH"),
    Timeout = TimeSpan.FromSeconds(30),  // Request timeout
    Retries = 3                          // Number of retry attempts
});
```

## Authorization Checks

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

### Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```csharp
oso.Authorize(actor: Value, action: string, resource: Value, contextFacts: Fact[] = null): bool
```

**Basic Example:**
```csharp
using OsoCloud;

var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
var canRead = oso.Authorize(alice, "read", repo);
Console.WriteLine($"Alice can read repo: {canRead}");

// Check if Alice can write to the repository
var canWrite = oso.Authorize(alice, "write", repo);
Console.WriteLine($"Alice can write to repo: {canWrite}");
```

**With Context Facts:**
```csharp
// Check authorization with temporary context
var contextFacts = new Fact[] { new Fact("is_public", repo) };
var canReadPublic = oso.Authorize(alice, "read", repo, contextFacts);
```

### List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```csharp
oso.List(actor: Value, action: string, resourceType: string, contextFacts: Fact[] = null): List<string>
```

**Example:**
```csharp
var alice = new Value("User", "alice");

// Get all repositories Alice can read
var readableRepos = oso.List(alice, "read", "Repository");
Console.WriteLine($"Readable repositories: {string.Join(", ", readableRepos)}");

// Get all organizations Alice can admin
var adminOrgs = oso.List(alice, "admin", "Organization");
Console.WriteLine($"Admin organizations: {string.Join(", ", adminOrgs)}");
```

**With Context Facts:**
```csharp
// List with additional context
var contextFacts = new Fact[] { new Fact("user_preference", alice, new Value("show_public"), new Value(true)) };
var publicRepos = oso.List(alice, "read", "Repository", contextFacts);
```

### List Actions

Fetches all actions that an actor can perform on a specific resource.

```csharp
oso.Actions(actor: Value, resource: Value, contextFacts: Fact[] = null): List<string>
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
var allowedActions = oso.Actions(alice, repo);
Console.WriteLine($"Allowed actions: {string.Join(", ", allowedActions)}");
// Output: ["read", "write"] or ["read", "write", "admin"]
```

### Advanced Querying

For complex authorization queries, use the simple query interface.

```csharp
oso.Query(rule: QueryFact): QueryResult[]
```

**Basic Query:**
```csharp
// Find all users who can admin a specific repository
var adminUsers = oso.Query(new QueryFact("allow", new Variable("User"), "admin", repo));

Console.WriteLine($"Admin users: {string.Join(", ", adminUsers)}");
```

**Complex Query with Type Constraints:**
```csharp
// Find all repositories that Alice can read
var readableRepos = oso.Query(new QueryFact("allow", alice, "read", new Variable("Repository")));

Console.WriteLine($"Readable repositories: {string.Join(", ", readableRepos)}");
```

**Note:** .NET uses a simple query interface without the QueryBuilder pattern.

## Facts Management

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

### Insert Facts

Adds a single fact to the centralized authorization data store.

```csharp
oso.Insert(fact: Fact): void
```

**Example:**
```csharp
using OsoCloud;

// Create entity values
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");
var org = new Value("Organization", "acme");

// Insert role assignment
oso.Insert(new Fact("has_role", alice, "admin", repo));

// Insert organization membership
oso.Insert(new Fact("member_of", alice, org));

// Insert resource property
oso.Insert(new Fact("is_public", repo));
```

### Batch Operations

Perform multiple fact operations atomically using transactions.

```csharp
oso.Batch(operations: BatchOperation[]): void
```

**Example:**
```csharp
// Atomic batch operation
var operations = new BatchOperation[]
{
    // Remove all existing roles for Alice
    new DeleteOperation(new Fact("has_role", alice, null, null)),
    
    // Add new role assignments
    new InsertOperation(new Fact("has_role", alice, "admin", repo1)),
    new InsertOperation(new Fact("has_role", alice, "member", repo2)),
    new InsertOperation(new Fact("member_of", alice, org))
};

oso.Batch(operations);
```

### Get Facts

Retrieve facts from the authorization data store using pattern matching.

```csharp
oso.Get(pattern: Fact): List<Fact>
```

**Example:**
```csharp
// Get all roles for Alice
var aliceRoles = oso.Get(new Fact("has_role", alice, null, null));
Console.WriteLine($"Alice's roles: {aliceRoles.Count}");

// Get all members of an organization
var orgMembers = oso.Get(new Fact("member_of", null, org));
Console.WriteLine($"Organization members: {orgMembers.Count}");

// Get specific fact
var isPublic = oso.Get(new Fact("is_public", repo));
Console.WriteLine($"Repository is public: {isPublic.Count > 0}");
```

### Delete Facts

Remove facts from the authorization data store.

```csharp
oso.Delete(pattern: Fact): void
```

**Example:**
```csharp
// Delete specific role assignment
oso.Delete(new Fact("has_role", alice, "admin", repo));

// Delete all roles for Alice on any repository
oso.Delete(new Fact("has_role", alice, null, null));

// Delete all facts about a repository
oso.Delete(new Fact(null, null, repo));
```

## Policy Management

Deploy and manage your Polar authorization policies in Oso Cloud.

### Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```csharp
oso.Policy(policy: string): void
```

**Example:**
```csharp
try
{
    var policy = @"
        actor User {}
        resource Repository {
            permissions = [""read"", ""write"", ""admin""];
            roles = [""member"", ""maintainer"", ""admin""];
            
            ""member"" if ""maintainer"";
            ""maintainer"" if ""admin"";
        }
        
        allow(user: User, ""read"", repo: Repository) if
            has_role(user, ""member"", repo);
            
        allow(user: User, ""write"", repo: Repository) if
            has_role(user, ""maintainer"", repo);
            
        allow(user: User, ""admin"", repo: Repository) if
            has_role(user, ""admin"", repo);
            
        test ""basic permissions"" {
            setup {
                has_role(User{""alice""}, ""member"", Repository{""widgets""});
            }
            
            assert allow(User{""alice""}, ""read"", Repository{""widgets""});
            assert_not allow(User{""alice""}, ""write"", Repository{""widgets""});
        }
    ";
    
    oso.Policy(policy);
    Console.WriteLine("Policy deployed successfully!");
}
catch (Exception ex)
{
    Console.WriteLine($"Policy deployment failed: {ex.Message}");
}
```

### Get Policy

Retrieve the currently deployed policy.

```csharp
oso.GetPolicy(): string
```

**Example:**
```csharp
var currentPolicy = oso.GetPolicy();
Console.WriteLine($"Current policy: {currentPolicy}");
```

### Policy Stats

Get statistics about your deployed policy.

```csharp
oso.Stats(): PolicyStats
```

**Example:**
```csharp
var stats = oso.Stats();
Console.WriteLine($"Policy statistics: NumRules={stats.NumRules}, NumTypes={stats.NumTypes}, NumResources={stats.NumResources}");
```

## Local Authorization

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

### List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```csharp
oso.ListLocal(actor: Value, action: string, resourceType: string, column: string): string
```

**Basic Example:**
```csharp
using OsoCloud;

var alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
var sqlFilter = oso.ListLocal(alice, "read", "Repository", "id");
Console.WriteLine($"SQL Filter: {sqlFilter}");
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Entity Framework):**
```csharp
using Microsoft.EntityFrameworkCore;

public async Task<List<Repository>> GetReadableRepositoriesAsync(string userId)
{
    var user = new Value("User", userId);
    var filter = oso.ListLocal(user, "read", "Repository", "Id");
    
    // Use the filter in your database query
    var repositories = await _context.Repositories
        .FromSqlRaw($"SELECT * FROM Repositories WHERE {filter}")
        .ToListAsync();
        
    return repositories;
}
```

**Advanced Usage:**
```csharp
// Get repositories Alice can admin with additional filtering
var adminFilter = oso.ListLocal(alice, "admin", "Repository", "Id");
var repositories = await _context.Repositories
    .FromSqlRaw($"SELECT * FROM Repositories WHERE {adminFilter}")
    .Where(r => r.Active == true)
    .Where(r => r.CreatedAt > new DateTime(2023, 1, 1))
    .ToListAsync();
```

### Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```csharp
oso.AuthorizeLocal(actor: Value, action: string, resource: Value, column: string): string
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get SQL condition for checking if Alice can read the repository
var condition = oso.AuthorizeLocal(alice, "read", repo, "Id");
Console.WriteLine($"SQL Condition: {condition}");
// Output: "Id = 'acme/widgets'"

// Use in a database query
var repository = await _context.Repositories
    .FromSqlRaw($"SELECT * FROM Repositories WHERE {condition}")
    .FirstOrDefaultAsync();
```

## Migration Guide

### Stable API

.NET client library has a stable API with no breaking changes. The current version provides:

- **Consistent Interface**: All methods maintain backward compatibility
- **Strong Typing**: Full support for .NET type system
- **Async/Await**: Asynchronous operations where appropriate

### Best Practices

#### Error Handling
```csharp
using OsoCloud.Exceptions;

public async Task<bool> CheckAuthorizationAsync(Value user, string action, Value resource)
{
    try
    {
        return oso.Authorize(user, action, resource);
    }
    catch (PolicyNotFoundException)
    {
        Console.WriteLine("No policy deployed");
        return false; // Fail closed
    }
    catch (InvalidActorException)
    {
        Console.WriteLine("Invalid user format");
        throw new ArgumentException("Authentication required");
    }
    catch (OsoException ex)
    {
        Console.WriteLine($"Authorization check failed: {ex.Message}");
        throw; // Re-throw unexpected errors
    }
}
```

#### Dependency Injection (ASP.NET Core)
```csharp
// Program.cs or Startup.cs
services.AddSingleton<IOsoClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    return new OsoClient(new OsoConfig
    {
        Url = "https://cloud.osohq.com",
        ApiKey = configuration["Oso:ApiKey"]
    });
});

services.AddScoped<IAuthorizationService, AuthorizationService>();

// AuthorizationService.cs
public class AuthorizationService : IAuthorizationService
{
    private readonly IOsoClient _oso;
    
    public AuthorizationService(IOsoClient oso)
    {
        _oso = oso;
    }
    
    public bool Authorize(ClaimsPrincipal user, string action, object resource)
    {
        var actor = new Value("User", user.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var resourceValue = new Value(resource.GetType().Name, GetResourceId(resource));
        
        return _oso.Authorize(actor, action, resourceValue);
    }
    
    private string GetResourceId(object resource)
    {
        // Extract ID from resource object
        var idProperty = resource.GetType().GetProperty("Id");
        return idProperty?.GetValue(resource)?.ToString();
    }
}

// Controller usage
[ApiController]
[Route("api/[controller]")]
public class RepositoriesController : ControllerBase
{
    private readonly IAuthorizationService _authService;
    
    public RepositoriesController(IAuthorizationService authService)
    {
        _authService = authService;
    }
    
    [HttpGet("{id}")]
    public async Task<IActionResult> GetRepository(string id)
    {
        var repository = await _repositoryService.GetByIdAsync(id);
        
        if (!_authService.Authorize(User, "read", repository))
        {
            return Forbid();
        }
        
        return Ok(repository);
    }
}
```

#### Caching
```csharp
using Microsoft.Extensions.Caching.Memory;

public class CachedAuthorizationService : IAuthorizationService
{
    private readonly IOsoClient _oso;
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);
    
    public CachedAuthorizationService(IOsoClient oso, IMemoryCache cache)
    {
        _oso = oso;
        _cache = cache;
    }
    
    public bool Authorize(Value user, string action, Value resource)
    {
        var key = $"auth:{user.Id}:{action}:{resource.Id}";
        
        if (_cache.TryGetValue(key, out bool cached))
        {
            return cached;
        }
        
        var result = _oso.Authorize(user, action, resource);
        _cache.Set(key, result, _cacheExpiry);
        return result;
    }
}
```

## Performance Considerations

### HTTP Client Configuration
```csharp
// Configure HttpClient for optimal performance
services.AddHttpClient<IOsoClient, OsoClient>(client =>
{
    client.BaseAddress = new Uri("https://cloud.osohq.com");
    client.Timeout = TimeSpan.FromSeconds(30);
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
{
    MaxConnectionsPerServer = 10
});
```

### Batch Operations
```csharp
// Instead of multiple individual checks
var canRead = oso.Authorize(alice, "read", repo1);
var canWrite = oso.Authorize(alice, "write", repo1);
var canAdmin = oso.Authorize(alice, "admin", repo1);

// Use Actions() for multiple permission checks on same resource
var allowedActions = oso.Actions(alice, repo1);
var canRead = allowedActions.Contains("read");
var canWrite = allowedActions.Contains("write");
var canAdmin = allowedActions.Contains("admin");
```

### Async Operations
```csharp
// Use async versions for better scalability
public async Task<bool> AuthorizeAsync(Value user, string action, Value resource)
{
    return await Task.Run(() => oso.Authorize(user, action, resource));
}

// Parallel authorization checks
var tasks = resources.Select(async resource => 
    new { Resource = resource, Allowed = await AuthorizeAsync(user, "read", resource) });

var results = await Task.WhenAll(tasks);
var allowedResources = results.Where(r => r.Allowed).Select(r => r.Resource);
```

## Testing

### Unit Testing with Moq
```csharp
using Moq;
using Xunit;

public class AuthorizationServiceTests
{
    private readonly Mock<IOsoClient> _mockOso;
    private readonly AuthorizationService _service;
    
    public AuthorizationServiceTests()
    {
        _mockOso = new Mock<IOsoClient>();
        _service = new AuthorizationService(_mockOso.Object);
    }
    
    [Fact]
    public void Authorize_WhenAllowed_ReturnsTrue()
    {
        // Arrange
        var user = new Value("User", "alice");
        var resource = new Value("Repository", "repo1");
        
        _mockOso.Setup(x => x.Authorize(user, "read", resource))
               .Returns(true);
        
        // Act
        var result = _service.Authorize(user, "read", resource);
        
        // Assert
        Assert.True(result);
        _mockOso.Verify(x => x.Authorize(user, "read", resource), Times.Once);
    }
    
    [Fact]
    public void Authorize_WhenNotAllowed_ReturnsFalse()
    {
        // Arrange
        var user = new Value("User", "alice");
        var resource = new Value("Repository", "repo1");
        
        _mockOso.Setup(x => x.Authorize(user, "read", resource))
               .Returns(false);
        
        // Act
        var result = _service.Authorize(user, "read", resource);
        
        // Assert
        Assert.False(result);
    }
}
```

## Next Steps

- [Policy Patterns](/develop/policies/patterns) - Learn common authorization patterns
- [Performance Guide](/develop/troubleshooting/query-performance) - Optimize authorization performance
- [HTTP API Reference](/reference/api/check-api) - Direct API access
