---
title: Python Client Library
description: "Complete guide to using the Oso Cloud Python client library for authorization."
sidebarTitle: "Python"
---

## Installation

Install the Oso Cloud Python client:

```bash
pip install oso-cloud
```

**Requirements:**
- Python 3.8 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```python
from oso_cloud import Oso

oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"]
)
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```python
oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"],
    timeout=30.0,  # Request timeout in seconds
    retries=3      # Number of retry attempts
)
```

## Authorization Checks

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

### Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```python
oso.authorize(actor: Value, action: str, resource: Value, context_facts: list[tuple] = None) -> bool
```

**Basic Example:**
```python
from oso_cloud import Value

alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Check if Alice can read the repository
can_read = oso.authorize(alice, "read", repo)
print(f"Alice can read repo: {can_read}")

# Check if Alice can write to the repository
can_write = oso.authorize(alice, "write", repo)
print(f"Alice can write to repo: {can_write}")
```

**With Context Facts:**
```python
# Check authorization with temporary context
can_read_public = oso.authorize(
    alice, 
    "read", 
    repo, 
    [("is_public", repo)]
)
```

### List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```python
oso.list(actor: Value, action: str, resource_type: str, context_facts: list[tuple] = None) -> List[str]
```

**Example:**
```python
alice = Value("User", "alice")

# Get all repositories Alice can read
readable_repos = oso.list(alice, "read", "Repository")
print("Readable repositories:", readable_repos)

# Get all organizations Alice can admin
admin_orgs = oso.list(alice, "admin", "Organization")
print("Admin organizations:", admin_orgs)
```

**With Context Facts:**
```python
# List with additional context
public_repos = oso.list(
    alice, 
    "read", 
    "Repository",
    [("user_preference", alice, "show_public", True)]
)
```

### List Actions

Fetches all actions that an actor can perform on a specific resource.

```python
oso.actions(actor: Value, resource: Value, context_facts: list[tuple] = None) -> List[str]
```

**Example:**
```python
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Get all actions Alice can perform on the repository
allowed_actions = oso.actions(alice, repo)
print("Allowed actions:", allowed_actions)
# Output: ["read", "write"] or ["read", "write", "admin"]
```

### Advanced Querying

For complex authorization queries, use the Query Builder API to construct sophisticated queries with multiple conditions.

```python
oso.build_query(predicate: tuple[str, ...]) -> QueryBuilder
```

**Basic Query:**
```python
# Find all users who can admin a specific repository
admin_users = oso.build_query(("allow", oso.var("user"), "admin", repo)).evaluate(oso.var("user"))

print("Admin users:", admin_users)
```

**Complex Query with Conditions:**
```python
# Find all repositories in the "acme" organization that Alice can read
acme_org = Value("Organization", "acme")
readable_acme_repos = (oso.build_query(("allow", alice, "read", oso.var("repo")))
                      .and_(("parent", oso.var("repo"), acme_org))
                      .evaluate(oso.var("repo")))

print("Readable acme repositories:", readable_acme_repos)
```

**Query with Context Facts:**
```python
# Query with temporary context
context_query = (oso.build_query(("allow", alice, "read", oso.var("repo")))
                 .with_context_facts([("is_public", oso.var("repo"))])
                 .evaluate(oso.var("repo")))
```

**Migration from v1:**
```python
# Old (v1)
results = oso.query("allow", alice, "read", oso.var("repo"))

# New (v2)
results = oso.build_query(("allow", alice, "read", oso.var("repo"))).evaluate(oso.var("repo"))
```

## Facts Management

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

### Insert Facts

Adds a single fact to the centralized authorization data store.

```python
oso.insert(fact: tuple[str, ...]) -> None
```

**Example:**
```python
from oso_cloud import Value

# Create entity values
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")
org = Value("Organization", "acme")

# Insert role assignment
oso.insert(("has_role", alice, "admin", repo))

# Insert organization membership
oso.insert(("member_of", alice, org))

# Insert resource property
oso.insert(("is_public", repo))
```

**Migration from v1:**
```python
# Old (v1)
oso.tell("has_role", alice, "admin", repo)

# New (v2)  
oso.insert(("has_role", alice, "admin", repo))
```

### Batch Operations

Perform multiple fact operations atomically using transactions.

```python
oso.batch(callback: Callable[[Transaction], None]) -> None
```

**Example:**
```python
# Atomic batch operation
def update_user_roles(tx):
    # Remove all existing roles for Alice
    tx.delete(("has_role", alice, None, None))
    
    # Add new role assignments
    tx.insert(("has_role", alice, "admin", repo1))
    tx.insert(("has_role", alice, "member", repo2))
    tx.insert(("member_of", alice, org))

oso.batch(update_user_roles)
```

### Get Facts

Retrieve facts from the authorization data store using pattern matching.

```python
oso.get(pattern: tuple[str, ...]) -> List[tuple]
```

**Example:**
```python
# Get all roles for Alice
alice_roles = oso.get(("has_role", alice, None, None))
print("Alice's roles:", alice_roles)

# Get all members of an organization
org_members = oso.get(("member_of", None, org))
print("Organization members:", org_members)

# Get specific fact
is_public = oso.get(("is_public", repo))
print("Repository is public:", len(is_public) > 0)
```

### Delete Facts

Remove facts from the authorization data store.

```python
oso.delete(pattern: tuple[str, ...]) -> None
```

**Example:**
```python
# Delete specific role assignment
oso.delete(("has_role", alice, "admin", repo))

# Delete all roles for Alice on any repository
oso.delete(("has_role", alice, None, None))

# Delete all facts about a repository
oso.delete((None, None, repo))
```

## Policy Management

Deploy and manage your Polar authorization policies in Oso Cloud.

### Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```python
oso.policy(policy: str) -> None
```

**Example:**
```python
try:
    oso.policy("""
        actor User {}
        resource Repository {
            permissions = ["read", "write", "admin"];
            roles = ["member", "maintainer", "admin"];
            
            "member" if "maintainer";
            "maintainer" if "admin";
        }
        
        allow(user: User, "read", repo: Repository) if
            has_role(user, "member", repo);
            
        allow(user: User, "write", repo: Repository) if
            has_role(user, "maintainer", repo);
            
        allow(user: User, "admin", repo: Repository) if
            has_role(user, "admin", repo);
            
        test "basic permissions" {
            setup {
                has_role(User{"alice"}, "member", Repository{"widgets"});
            }
            
            assert allow(User{"alice"}, "read", Repository{"widgets"});
            assert_not allow(User{"alice"}, "write", Repository{"widgets"});
        }
    """)
    
    print("Policy deployed successfully!")
except Exception as error:
    print(f"Policy deployment failed: {error}")
```

### Get Policy

Retrieve the currently deployed policy.

```python
oso.get_policy() -> str
```

**Example:**
```python
current_policy = oso.get_policy()
print("Current policy:", current_policy)
```

### Policy Stats

Get statistics about your deployed policy.

```python
oso.stats() -> PolicyStats
```

**Example:**
```python
stats = oso.stats()
print("Policy statistics:", {
    "num_rules": stats.num_rules,
    "num_types": stats.num_types,
    "num_resources": stats.num_resources
})
```

## Local Authorization

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

### List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```python
oso.list_local(actor: Value, action: str, resource_type: str, column: str) -> str
```

**Basic Example:**
```python
from oso_cloud import Value

alice = Value("User", "alice")

# Get SQL filter for repositories Alice can read
sql_filter = oso.list_local(alice, "read", "Repository", "id")
print("SQL Filter:", sql_filter)
# Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (SQLAlchemy):**
```python
from sqlalchemy import text

def get_readable_repositories(user_id: str):
    user = Value("User", user_id)
    filter_sql = oso.list_local(user, "read", "Repository", "id")
    
    # Use the filter in your database query
    query = text(f"SELECT * FROM repositories WHERE {filter_sql}")
    repositories = session.execute(query).fetchall()
    
    return repositories
```

**Advanced Usage:**
```python
# Get repositories Alice can admin with additional filtering
admin_filter = oso.list_local(alice, "admin", "Repository", "id")
query = text(f"""
    SELECT * FROM repositories 
    WHERE {admin_filter} 
    AND active = true 
    AND created_at > '2023-01-01'
""")
repositories = session.execute(query).fetchall()
```

### Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```python
oso.authorize_local(actor: Value, action: str, resource: Value, column: str) -> str
```

**Example:**
```python
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Get SQL condition for checking if Alice can read the repository
condition = oso.authorize_local(alice, "read", repo, "id")
print("SQL Condition:", condition)
# Output: "id = 'acme/widgets'"

# Use in a database query
query = text(f"SELECT * FROM repositories WHERE {condition}")
repository = session.execute(query).fetchone()
```

## Migration Guide

### Breaking Changes (v1 → v2)

#### Value Objects
All entity references now use `Value` objects instead of dictionaries.

```python
# Old (v1)
user = {"type": "User", "id": "alice"}

# New (v2)
user = Value("User", "alice")
```

#### Tuple Facts
Facts are now represented as tuples instead of individual arguments.

```python
# Old (v1)
oso.tell("has_role", user, "admin", repo)

# New (v2)
oso.insert(("has_role", user, "admin", repo))
```

#### Enhanced QueryBuilder
The query API has been completely redesigned.

```python
# Old (v1)
results = oso.query("allow", user, "read", oso.var("repo"))

# New (v2)
results = oso.build_query(("allow", user, "read", oso.var("repo"))).evaluate(oso.var("repo"))
```

### New Features

#### Type Hints
Full type hint support for better IDE integration:

```python
from typing import List
from oso_cloud import Value, Fact

def check_permissions(user: Value, resources: List[Value]) -> List[str]:
    allowed_actions: List[str] = []
    for resource in resources:
        actions = oso.actions(user, resource)
        allowed_actions.extend(actions)
    return allowed_actions
```

#### Context Manager Support
Use Oso client as a context manager for automatic cleanup:

```python
with Oso(url="https://cloud.osohq.com", api_key=api_key) as oso:
    result = oso.authorize(user, "read", resource)
    print(f"Authorized: {result}")
```

## Error Handling

```python
from oso_cloud import OsoError, PolicyNotFoundError, InvalidActorError

try:
    allowed = oso.authorize(user, action, resource)
    return allowed
except PolicyNotFoundError:
    print("No policy deployed")
    return False  # Fail closed
except InvalidActorError:
    print("Invalid user format")
    raise ValueError("Authentication required")
except OsoError as error:
    print(f"Authorization check failed: {error}")
    raise error  # Re-throw unexpected errors
```

## Performance Considerations

### Caching
```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def cached_authorize(user_id: str, action: str, resource_id: str) -> bool:
    user = Value("User", user_id)
    resource = Value("Repository", resource_id)
    return oso.authorize(user, action, resource)
```

### Batch Operations
```python
# Instead of multiple individual checks
can_read = oso.authorize(alice, "read", repo1)
can_write = oso.authorize(alice, "write", repo1)
can_admin = oso.authorize(alice, "admin", repo1)

# Use actions() for multiple permission checks on same resource
allowed_actions = oso.actions(alice, repo1)
can_read = "read" in allowed_actions
can_write = "write" in allowed_actions
can_admin = "admin" in allowed_actions
```

## Next Steps

- [Policy Patterns](/develop/policies/patterns) - Learn common authorization patterns
- [Performance Guide](/develop/troubleshooting/query-performance) - Optimize authorization performance
- [HTTP API Reference](/reference/api/check-api) - Direct API access
