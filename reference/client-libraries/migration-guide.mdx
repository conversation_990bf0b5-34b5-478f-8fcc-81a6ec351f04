---
title: Migration Guide
description: "This guide helps you migrate your Oso Cloud client libraries to the latest versions. "
sidebarTitle: "Migration guide"
---

The Oso Cloud client libraries have evolved to provide better performance, enhanced type safety, and more intuitive APIs. Here's what's changed across different versions:

Each language has different migration requirements and breaking changes, so select your language below for specific migration instructions.

### Major Changes Summary

| Language | Version | Key Changes |
|----------|---------|-------------|
| **Node.js** | v1 → v2 | Node.js 16+, array-wrapped arguments, QueryBuilder API |
| **Python** | v1 → v2 | Value objects, tuple facts, enhanced QueryBuilder |
| **Go** | v1 → v2 | Import path `/v2`, Instance → Value, helper functions |
| **Java** | v0 → v1 | Package consolidation, ValuePattern, sophisticated QueryBuilder |
| **Ruby** | Stable | No breaking changes - mature API |
| **C#/.NET** | Stable | No breaking changes - mature API |

## Migrations by Language

<Tabs>
<Tab title="Node.js">

### Breaking Changes

#### Node.js Version Requirement
Upgrade your Node.js runtime to version 16 or later.

```json package.json
{
  "engines": {
    "node": ">=16.0.0"
  }
}
```

#### Facts API Changes
All fact operations now use array-wrapped arguments instead of individual parameters.

```javascript Facts API (v2)
// New insert() method with array wrapping
await oso.insert(["has_role", user, "admin", repo]);

// New delete() method with array wrapping
await oso.delete(["has_role", user, "admin", repo]);

// New get() method with array wrapping
const facts = await oso.get(["has_role", user, null, null]);
```

#### Batch Operations
The `bulk()` method has been replaced with a transaction-based `batch()` API.

```javascript Batch API (v2) 
await oso.batch((tx) => {
  tx.delete(["has_role", user, null, null]);
  tx.insert(["has_role", user, "admin", repo]);
});
```

#### Query API
The `query()` method has been replaced with `buildQuery().evaluate()`.

```javascript Query API (v2) 
const results = await oso.buildQuery(["has_role", user, "admin", repo])
  .evaluate();
```

### New Features

#### TypeScript Type Generation
Generate TypeScript types from your Polar policies:

```bash CLI commands 
# Install CLI
npm install -g oso-cloud-cli

# Generate types
oso-cloud generate-types --output ./src/types/oso-types.ts
```

```typescript Type usage 
import { User, Repository } from './types/oso-types';

const user: User = { type: "User", id: "alice" };
const repo: Repository = { type: "Repository", id: "acme/widgets" };

// TypeScript will enforce correct types
const allowed = await oso.authorize(user, "read", repo);
```

#### Enhanced QueryBuilder
Complex queries with fluent interface and context facts:

```javascript QueryBuilder with context facts 
const users = await oso.buildQuery(["allow", oso.var("user"), "admin", oso.var("repo")])
  .and(["parent", oso.var("repo"), organization])
  .withContextFacts([["is_active", oso.var("user")]])
  .evaluate(oso.var("user"));
```

### Migration Steps

1. **Update dependencies**: `npm install oso-cloud@^2.0.0`
2. **Ensure Node.js 16+**: `node --version`
3. **Update fact operations**: Replace `tell/delete/get` with array-wrapped `insert/delete/get`
4. **Update batch operations**: Replace `bulk()` with `batch()`
5. **Update query API**: Replace `query()` with `buildQuery().evaluate()`
6. **Test your changes**: `npm test`

</Tab>

<Tab title="Python">

### Breaking Changes

#### Value Representation
Entities must now use `Value` objects instead of dictionaries.

```python Entity representation (v2)
from oso_cloud import Value

# Value objects
user = Value("User", "alice")
repo = Value("Repository", "acme/widgets")
```

#### Fact Representation
Facts are now tuples instead of dictionaries.

```python Fact representation (v2)
# Tuple format for facts
context_fact = ("has_role", user, "member", repo)
```

#### Facts API Changes
All fact operations use tuple format.

```python Facts API (v2)
# New insert() method with tuple format
oso.insert(("has_role", user, "admin", repo))

# New delete() method with tuple format
oso.delete(("has_role", user, "admin", repo))

# New get() method with tuple format
facts = oso.get(("has_role", user, None, None))
```

#### Query API
The `query()` method has been replaced with `build_query().evaluate()`.

```python Query API (v2)
results = oso.build_query(("allow", actor, "read", repository)).evaluate(repository)
```

### New Features

#### Enhanced QueryBuilder API
Complex queries with fluent interface:

```python QueryBuilder with context facts 
users = (oso.build_query(("allow", oso.var("user"), "admin", oso.var("repo")))
         .and_(("parent", oso.var("repo"), organization))
         .with_context_facts([("is_active", oso.var("user"))])
         .evaluate(oso.var("user")))
```

#### Context Manager for Batch Operations
Automatic batching with context manager:

```python Batch context manager
with oso.batch() as tx:
    tx.delete(("has_role", user, "member", repo))
    tx.insert(("has_role", user, "admin", repo))
    tx.insert(("can_invite", user, repo))
```

#### Enhanced Wildcard Support
Type-constrained wildcards:

```python Wildcards
from oso_cloud import ValueOfType

# Type-constrained wildcards
oso.delete(("has_role", None, "admin", ValueOfType("Repository")))
facts = oso.get(("has_role", user, None, ValueOfType("Organization")))
```

### Migration Steps

1. **Update dependencies**: `pip install oso-cloud>=2.0.0`
2. **Update imports**: `from oso_cloud import OsoCloud, Value, ValueOfType`
3. **Convert entities**: Replace dict entities with `Value()` objects
4. **Convert facts**: Replace dict facts with tuples
5. **Update method calls**: Replace `tell()` with `insert()` using tuple format
6. **Update query calls**: Replace `query()` with `build_query().evaluate()`

</Tab>

<Tab title="Go">

### Breaking Changes

#### Import Path Change
Module path changed to `/v2`.

```go Import path (v2)
import (
    oso "github.com/osohq/go-oso-cloud/v2"
)
```

#### Struct Changes
`Instance` struct replaced with `NewValue()` helper function.

```go Value construction (v2)
// Value with helper function
user := oso.NewValue("User", "alice")
```

#### Field Changes
`Fact.Name` field renamed to `Fact.Predicate`.

```go Field access (v2)
// Fact.Predicate field
factName := fact.Predicate
```

#### API Changes
Tell/Delete/Get methods now use helper functions.

```go Facts API (v2)
// Insert method with NewFact helper
err := osoClient.Insert(oso.NewFact("has_role", user, role, repo))

// Delete method with NewFactPattern helper
err = osoClient.Delete(oso.NewFactPattern("has_role", user, role, repo))

// Get method with NewFactPattern helper
facts, err := osoClient.Get(oso.NewFactPattern("has_role", user, nil, nil))
```

#### Query API
Query method now uses `BuildQuery()` with helper functions.

```go Query API (v2)
results, err := osoClient.BuildQuery(oso.NewQueryFact("allow", user, oso.String("read"), repo)).EvaluateValues(repo)
```

### New Features

#### Helper Functions
Ergonomic construction helpers:

```go Helper functions
fact := oso.NewFact("has_role", user, "admin", repo)
pattern := oso.NewFactPattern("has_role", user, nil, oso.NewValueOfType("Repository"))
query := oso.NewQueryFact("allow", oso.Variable("user"), oso.String("read"), repo)
```

#### Enhanced Wildcard Support
Type constraints and wildcard deletion:

```go Wildcards
// Type constraints
pattern := oso.NewFactPattern("has_role", nil, "admin", oso.NewValueOfType("Repository"))
facts, err := osoClient.Get(pattern)

// Wildcard deletion
deletePattern := oso.NewFactPattern("has_role", user, nil, nil)
```

#### Batch Operations
Batch operations API:

```go Batch operations
operations := []oso.BatchOperation{
    oso.NewDeleteOperation(oso.NewFactPattern("has_role", user, "member", repo)),
    oso.NewInsertOperation(oso.NewFact("has_role", user, "admin", repo)),
}

err := osoClient.Batch(operations)
```

### Migration Steps

1. **Update module**: `go mod edit -replace github.com/osohq/go-oso-cloud=github.com/osohq/go-oso-cloud/v2@latest && go mod tidy`
2. **Update imports**: Change to `/v2` path
3. **Update value construction**: Replace `Instance{}` with `NewValue()`
4. **Update field references**: Change `fact.Name` to `fact.Predicate`
5. **Update fact operations**: Use `NewFact`/`NewFactPattern` helpers
6. **Update query calls**: Use `BuildQuery()` with `NewQueryFact`

</Tab>

<Tab title="Java">

### Breaking Changes

#### Package Structure
Packages consolidated - remove `.api` from import paths.

```java Imports (v1) 
import com.osohq.oso_cloud.Value;
import com.osohq.oso_cloud.Fact;
import com.osohq.oso_cloud.FactPattern;
import com.osohq.oso_cloud.ValuePattern;
```

#### Wildcard Handling
Explicit wildcard patterns replace null values.

```java Wildcards (v1)
import com.osohq.oso_cloud.ValuePattern;

// Explicit wildcard patterns
Fact[] facts = oso.get(new FactPattern("has_role", user, ValuePattern.ANY, ValuePattern.ANY));
```

#### API Changes
Tell/get methods now use Fact/FactPattern wrappers.

```java Facts API (v1) 
// New insert method with Fact wrapper
oso.insert(new Fact("has_role", user, new Value("admin"), repo));

// New get method with FactPattern
Fact[] facts = oso.get(new FactPattern("has_role", user, ValuePattern.ANY, ValuePattern.ANY));
```

#### Query API
Query method now uses `buildQuery().evaluate()`.

```java Query API (v1) 
String[] results = oso.buildQuery("allow", user, "read", repo)
                     .evaluate(EvaluateArgs.values(repo));
```

### New Features

#### Sophisticated QueryBuilder
Fluent interface with multiple evaluation options:

```java QueryBuilder
String[] users = oso.buildQuery("allow", oso.var("user"), "admin", oso.var("repo"))
                    .and("parent", oso.var("repo"), organization)
                    .evaluate(EvaluateArgs.values(oso.var("user")));

// Nested map evaluation for complex results
Map<String, Object>[] detailedResults = oso.buildQuery("allow", oso.var("user"), "read", oso.var("repo"))
                                          .evaluate(EvaluateArgs.nested(
                                              Map.of("user", oso.var("user"), "repo", oso.var("repo"))));
```

#### Enhanced Wildcard Support
Explicit wildcard patterns:

```java Wildcard patterns
// Explicit wildcard patterns
FactPattern pattern = new FactPattern("has_role", 
                                     ValuePattern.ANY, 
                                     new ValuePattern.Value("admin"),
                                     new ValuePattern.ValueOfType("Repository"));

Fact[] facts = oso.get(pattern);
```

#### EvaluateArgs System
Different evaluation strategies:

```java Evaluation strategies
String[] values = query.evaluate(EvaluateArgs.values(variable));
Map<String, Object>[] maps = query.evaluate(EvaluateArgs.maps());
Map<String, Object>[] nested = query.evaluate(EvaluateArgs.nested(structure));
```

### Migration Steps

1. **Update dependencies**: Update to latest `com.osohq:oso-cloud`
2. **Update imports**: Remove `.api` from import paths
3. **Update wildcard usage**: Replace `null` with `ValuePattern.ANY` or `ValueOfType`
4. **Update method calls**: Use `Fact`/`FactPattern` wrappers for `insert`/`get`
5. **Update query API**: Use `buildQuery().evaluate(...)`

</Tab>

<Tab title="Ruby">

### No Migration Needed

The Ruby SDK has maintained a stable API with no breaking changes. You can continue using your existing code:

```ruby Stable API
oso.policy(policy_string)
oso.tell("has_role", user, "admin", repo)
oso.authorize(user, "read", repo)
oso.list(user, "read", "Repository")
oso.actions(user, repo)
```

### Recommended Practices

#### Error Handling
Implement robust error handling:

```ruby Error handling
begin
  allowed = oso.authorize(user, action, resource)
rescue OsoCloud::Error => e
  Rails.logger.error "Authorization failed: #{e.message}"
  false # Fail closed
end
```

#### Performance Optimization
Use local authorization for list filtering:

```ruby Local authorization
class Repository < ApplicationRecord
  def self.readable_by(user_id)
    user = { type: "User", id: user_id }
    filter = $oso.list_local(user, "read", "Repository", "id")
    where(filter)
  end
end
```

</Tab>

<Tab title=".NET">

### No Migration Needed

The .NET SDK has maintained a stable API with no breaking changes. You can continue using your existing code:

```csharp Stable API
await oso.Policy(policyString);
await oso.Tell("has_role", new List<Value> { user, new Value("admin"), repo });
var allowed = oso.Authorize(user, "read", repo);
var resources = oso.List(user, "read", "Repository");
var actions = oso.Actions(user, repo);
```

### Recommended Practices

#### Error Handling
Implement robust error handling:

```csharp Error handling
try
{
    var allowed = oso.Authorize(user, action, resource);
    return allowed;
}
catch (OsoCloudException ex)
{
    _logger.LogError(ex, "Authorization failed");
    return false; // Fail closed
}
```

#### Performance Optimization
Use local authorization for list filtering:

```csharp Local authorization
public async Task<List<Repository>> GetReadableRepositoriesAsync(string userId)
{
    var user = new Value("User", userId);
    var filterResult = await _oso.ListLocal(user, "read", "Repository", "Id");
    
    return await _context.Repositories
        .FromSqlRaw($"SELECT * FROM Repositories WHERE {filterResult.Sql}")
        .ToListAsync();
}
```

</Tab>
</Tabs>
