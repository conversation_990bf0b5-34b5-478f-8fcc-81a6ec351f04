---
title: Go Client Library
description: "Complete guide to using the Oso Cloud Go client library for authorization."
sidebarTitle: "Go"
---

## Installation

Install the Oso Cloud Go client:

```bash
go get github.com/osohq/go-oso-cloud/v2
```

**Requirements:**
- Go 1.19 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```go
package main

import (
    "log"
    "os"
    oso "github.com/osohq/go-oso-cloud/v2"
)

func main() {
    osoClient, err := oso.NewClient(oso.Config{
        URL:    "https://cloud.osohq.com",
        APIKey: os.Getenv("OSO_AUTH"),
    })
    if err != nil {
        log.Fatal(err)
    }
    defer osoClient.Close()
}
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```go
osoClient, err := oso.NewClient(oso.Config{
    URL:     "https://cloud.osohq.com",
    APIKey:  os.Getenv("OSO_AUTH"),
    Timeout: 30 * time.Second,  // Request timeout
    Retries: 3,                 // Number of retry attempts
})
```

## Authorization Checks

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

### Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```go
osoClient.Authorize(actor Value, action string, resource Value) (bool, error)
osoClient.AuthorizeWithContextFacts(actor Value, action string, resource Value, contextFacts []Fact) (bool, error)
```

**Basic Example:**
```go
import oso "github.com/osohq/go-oso-cloud/v2"

alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Check if Alice can read the repository
canRead, err := osoClient.Authorize(alice, "read", repo)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Alice can read repo: %t\n", canRead)

// Check if Alice can write to the repository
canWrite, err := osoClient.Authorize(alice, "write", repo)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Alice can write to repo: %t\n", canWrite)
```

**With Context Facts:**
```go
// Check authorization with temporary context
contextFacts := []oso.Fact{oso.NewFact("is_public", repo)}
canReadPublic, err := osoClient.AuthorizeWithContextFacts(alice, "read", repo, contextFacts)
```

### List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```go
osoClient.List(actor Value, action string, resourceType string, contextFacts []Fact) ([]string, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")

// Get all repositories Alice can read
readableRepos, err := osoClient.List(alice, "read", "Repository", nil)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Readable repositories: %v\n", readableRepos)

// Get all organizations Alice can admin
adminOrgs, err := osoClient.List(alice, "admin", "Organization", nil)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Admin organizations: %v\n", adminOrgs)
```

**With Context Facts:**
```go
// List with additional context
contextFacts := []oso.Fact{oso.NewFact("user_preference", alice, "show_public", true)}
publicRepos, err := osoClient.List(alice, "read", "Repository", contextFacts)
```

### List Actions

Fetches all actions that an actor can perform on a specific resource.

```go
osoClient.Actions(actor Value, resource Value) ([]string, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Get all actions Alice can perform on the repository
allowedActions, err := osoClient.Actions(alice, repo)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Allowed actions: %v\n", allowedActions)
// Output: ["read", "write"] or ["read", "write", "admin"]
```

### Advanced Querying

For complex authorization queries, use the Query Builder API to construct sophisticated queries with multiple conditions.

```go
osoClient.BuildQuery(queryFact QueryFact) QueryBuilder
```

**Basic Query:**
```go
// Find all users who can admin a specific repository
adminUsers, err := osoClient.BuildQuery(oso.NewQueryFact("allow", oso.Variable("user"), oso.String("admin"), repo)).EvaluateValues(oso.Variable("user"))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Admin users: %v\n", adminUsers)
```

**Complex Query with Conditions:**
```go
// Find all repositories in the "acme" organization that Alice can read
acmeOrg := oso.NewValue("Organization", "acme")
readableAcmeRepos, err := osoClient.BuildQuery(oso.NewQueryFact("allow", alice, oso.String("read"), oso.Variable("repo"))).And(oso.NewQueryFact("parent", oso.Variable("repo"), acmeOrg)).EvaluateValues(oso.Variable("repo"))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Readable acme repositories: %v\n", readableAcmeRepos)
```

**Migration from v1:**
```go
// Old (v1)
results, err := osoClient.Query("allow", alice, "read", oso.Variable("repo"))

// New (v2)
results, err := osoClient.BuildQuery(oso.NewQueryFact("allow", alice, oso.String("read"), oso.Variable("repo"))).EvaluateValues(oso.Variable("repo"))
```

## Facts Management

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

### Insert Facts

Adds a single fact to the centralized authorization data store.

```go
osoClient.Insert(fact Fact) error
```

**Example:**
```go
import oso "github.com/osohq/go-oso-cloud/v2"

// Create entity values
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")
org := oso.NewValue("Organization", "acme")

// Insert role assignment
err := osoClient.Insert(oso.NewFact("has_role", alice, "admin", repo))
if err != nil {
    log.Fatal(err)
}

// Insert organization membership
err = osoClient.Insert(oso.NewFact("member_of", alice, org))
if err != nil {
    log.Fatal(err)
}

// Insert resource property
err = osoClient.Insert(oso.NewFact("is_public", repo))
if err != nil {
    log.Fatal(err)
}
```

**Migration from v1:**
```go
// Old (v1)
err := osoClient.Tell("has_role", alice, "admin", repo)

// New (v2)
err := osoClient.Insert(oso.NewFact("has_role", alice, "admin", repo))
```

### Batch Operations

Perform multiple fact operations atomically using transactions.

```go
osoClient.Batch(operations []BatchOperation) error
```

**Example:**
```go
// Atomic batch operation
operations := []oso.BatchOperation{
    // Remove all existing roles for Alice
    oso.NewDeleteOperation(oso.NewFact("has_role", alice, nil, nil)),
    
    // Add new role assignments
    oso.NewInsertOperation(oso.NewFact("has_role", alice, "admin", repo1)),
    oso.NewInsertOperation(oso.NewFact("has_role", alice, "member", repo2)),
    oso.NewInsertOperation(oso.NewFact("member_of", alice, org)),
}

err := osoClient.Batch(operations)
if err != nil {
    log.Fatal(err)
}
```

### Get Facts

Retrieve facts from the authorization data store using pattern matching.

```go
osoClient.Get(pattern Fact) ([]Fact, error)
```

**Example:**
```go
// Get all roles for Alice
aliceRoles, err := osoClient.Get(oso.NewFact("has_role", alice, nil, nil))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Alice's roles: %v\n", aliceRoles)

// Get all members of an organization
orgMembers, err := osoClient.Get(oso.NewFact("member_of", nil, org))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Organization members: %v\n", orgMembers)

// Get specific fact
isPublic, err := osoClient.Get(oso.NewFact("is_public", repo))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Repository is public: %t\n", len(isPublic) > 0)
```

### Delete Facts

Remove facts from the authorization data store.

```go
osoClient.Delete(pattern Fact) error
```

**Example:**
```go
// Delete specific role assignment
err := osoClient.Delete(oso.NewFact("has_role", alice, "admin", repo))
if err != nil {
    log.Fatal(err)
}

// Delete all roles for Alice on any repository
err = osoClient.Delete(oso.NewFact("has_role", alice, nil, nil))
if err != nil {
    log.Fatal(err)
}

// Delete all facts about a repository
err = osoClient.Delete(oso.NewFact(nil, nil, repo))
if err != nil {
    log.Fatal(err)
}
```

## Policy Management

Deploy and manage your Polar authorization policies in Oso Cloud.

### Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```go
osoClient.Policy(policy string) error
```

**Example:**
```go
policy := `
    actor User {}
    resource Repository {
        permissions = ["read", "write", "admin"];
        roles = ["member", "maintainer", "admin"];
        
        "member" if "maintainer";
        "maintainer" if "admin";
    }
    
    allow(user: User, "read", repo: Repository) if
        has_role(user, "member", repo);
        
    allow(user: User, "write", repo: Repository) if
        has_role(user, "maintainer", repo);
        
    allow(user: User, "admin", repo: Repository) if
        has_role(user, "admin", repo);
        
    test "basic permissions" {
        setup {
            has_role(User{"alice"}, "member", Repository{"widgets"});
        }
        
        assert allow(User{"alice"}, "read", Repository{"widgets"});
        assert_not allow(User{"alice"}, "write", Repository{"widgets"});
    }
`

err := osoClient.Policy(policy)
if err != nil {
    log.Fatalf("Policy deployment failed: %v", err)
}

fmt.Println("Policy deployed successfully!")
```

### Get Policy

Retrieve the currently deployed policy.

```go
osoClient.GetPolicy() (string, error)
```

**Example:**
```go
currentPolicy, err := osoClient.GetPolicy()
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Current policy: %s\n", currentPolicy)
```

### Policy Stats

Get statistics about your deployed policy.

```go
osoClient.Stats() (PolicyStats, error)
```

**Example:**
```go
stats, err := osoClient.Stats()
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Policy statistics: NumRules=%d, NumTypes=%d, NumResources=%d\n", 
    stats.NumRules, stats.NumTypes, stats.NumResources)
```

## Local Authorization

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

### List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```go
osoClient.ListLocal(actor Value, action string, resourceType string, column string) (string, error)
```

**Basic Example:**
```go
import oso "github.com/osohq/go-oso-cloud/v2"

alice := oso.NewValue("User", "alice")

// Get SQL filter for repositories Alice can read
sqlFilter, err := osoClient.ListLocal(alice, "read", "Repository", "id")
if err != nil {
    log.Fatal(err)
}
fmt.Printf("SQL Filter: %s\n", sqlFilter)
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (GORM):**
```go
import "gorm.io/gorm"

func getReadableRepositories(db *gorm.DB, userID string) ([]Repository, error) {
    user := oso.NewValue("User", userID)
    filter, err := osoClient.ListLocal(user, "read", "Repository", "id")
    if err != nil {
        return nil, err
    }
    
    var repositories []Repository
    err = db.Where(filter).Find(&repositories).Error
    return repositories, err
}
```

**Advanced Usage:**
```go
// Get repositories Alice can admin with additional filtering
adminFilter, err := osoClient.ListLocal(alice, "admin", "Repository", "id")
if err != nil {
    log.Fatal(err)
}

var repositories []Repository
err = db.Where(adminFilter).
    Where("active = ?", true).
    Where("created_at > ?", time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)).
    Find(&repositories).Error
```

### Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```go
osoClient.AuthorizeLocal(actor Value, action string, resource Value, column string) (string, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Get SQL condition for checking if Alice can read the repository
condition, err := osoClient.AuthorizeLocal(alice, "read", repo, "id")
if err != nil {
    log.Fatal(err)
}
fmt.Printf("SQL Condition: %s\n", condition)
// Output: "id = 'acme/widgets'"

// Use in a database query
var repository Repository
err = db.Where(condition).First(&repository).Error
```

## Migration Guide

### Breaking Changes (v1 → v2)

#### Import Path
Update your import path to include `/v2`:

```go
// Old (v1)
import oso "github.com/osohq/go-oso-cloud"

// New (v2)
import oso "github.com/osohq/go-oso-cloud/v2"
```

#### Instance → Value
The `Instance` type has been renamed to `Value`:

```go
// Old (v1)
user := oso.NewInstance("User", "alice")

// New (v2)
user := oso.NewValue("User", "alice")
```

#### Helper Functions
New helper functions for creating common types:

```go
// String values
action := oso.String("read")

// Variables for queries
userVar := oso.Variable("user")

// Nil values for patterns
pattern := oso.NewFact("has_role", user, nil, nil)
```

### New Features

#### Structured Error Handling
Better error types for different failure scenarios:

```go
import "github.com/osohq/go-oso-cloud/v2/errors"

_, err := osoClient.Authorize(user, action, resource)
if err != nil {
    switch e := err.(type) {
    case *errors.PolicyNotFoundError:
        log.Println("No policy deployed")
        return false // Fail closed
    case *errors.InvalidActorError:
        log.Println("Invalid user format")
        return errors.New("authentication required")
    default:
        log.Printf("Authorization check failed: %v", e)
        return err
    }
}
```

#### Context Support
All methods now support Go's context package:

```go
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

allowed, err := osoClient.AuthorizeWithContext(ctx, user, action, resource)
```

## Error Handling

```go
import (
    "context"
    "time"
    oso "github.com/osohq/go-oso-cloud/v2"
    "github.com/osohq/go-oso-cloud/v2/errors"
)

func checkAuthorization(user oso.Value, action string, resource oso.Value) (bool, error) {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    allowed, err := osoClient.AuthorizeWithContext(ctx, user, action, resource)
    if err != nil {
        switch e := err.(type) {
        case *errors.PolicyNotFoundError:
            log.Println("No policy deployed")
            return false, nil // Fail closed
        case *errors.InvalidActorError:
            return false, fmt.Errorf("authentication required: %w", e)
        case *errors.TimeoutError:
            return false, fmt.Errorf("authorization check timed out: %w", e)
        default:
            return false, fmt.Errorf("authorization check failed: %w", e)
        }
    }
    
    return allowed, nil
}
```

## Performance Considerations

### Connection Pooling
```go
// Configure connection pooling
osoClient, err := oso.NewClient(oso.Config{
    URL:            "https://cloud.osohq.com",
    APIKey:         os.Getenv("OSO_AUTH"),
    MaxConnections: 10,
    IdleTimeout:    30 * time.Second,
})
```

### Batch Operations
```go
// Instead of multiple individual checks
canRead, _ := osoClient.Authorize(alice, "read", repo1)
canWrite, _ := osoClient.Authorize(alice, "write", repo1)
canAdmin, _ := osoClient.Authorize(alice, "admin", repo1)

// Use Actions() for multiple permission checks on same resource
allowedActions, _ := osoClient.Actions(alice, repo1)
canRead := contains(allowedActions, "read")
canWrite := contains(allowedActions, "write")
canAdmin := contains(allowedActions, "admin")

func contains(slice []string, item string) bool {
    for _, s := range slice {
        if s == item {
            return true
        }
    }
    return false
}
```

## Next Steps

- [Policy Patterns](/develop/policies/patterns) - Learn common authorization patterns
- [Performance Guide](/develop/troubleshooting/query-performance) - Optimize authorization performance
- [HTTP API Reference](/reference/api/check-api) - Direct API access
