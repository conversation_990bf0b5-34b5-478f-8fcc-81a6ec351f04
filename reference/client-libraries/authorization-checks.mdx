---
title: Authorization Checks
description: "This page covers basic authorization checks, resource listing, action enumeration, and advanced querying capabilities."
sidebarTitle: "Authorization checks"
---

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource. 

Oso Cloud provides several types of authorization checks:

- **`authorize()`** - Check if an actor can perform an action on a resource
- **`list()`** - Get all resources an actor can perform an action on
- **`actions()`** - Get all actions an actor can perform on a resource
- **`buildQuery()`** - Advanced querying with flexible evaluation options

All methods support **context facts** - temporary facts that apply only to that specific authorization check.

## Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

<Tabs>
<Tab title="Node.js">

```javascript
oso.authorize(actor: Value, action: string, resource: Value, contextFacts?: Fact[]): Promise<boolean>
```

**Basic Example:**
```javascript
import { Value } from 'oso-cloud';

const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
const canRead = await oso.authorize(alice, "read", repo);
console.log(`Alice can read repo: ${canRead}`);

// Check if Alice can write to the repository
const canWrite = await oso.authorize(alice, "write", repo);
console.log(`Alice can write to repo: ${canWrite}`);
```

**With Context Facts:**
```javascript
// Check authorization with temporary context
const canReadPublic = await oso.authorize(
  alice, 
  "read", 
  repo, 
  [["is_public", repo]]
);
```

</Tab>
<Tab title="Python">

```python
oso.authorize(actor: Value, action: str, resource: Value, context_facts: list[tuple] = None) -> bool
```

**Basic Example:**
```python
from oso_cloud import Value

alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Check if Alice can read the repository
can_read = oso.authorize(alice, "read", repo)
print(f"Alice can read repo: {can_read}")

# Check if Alice can write to the repository
can_write = oso.authorize(alice, "write", repo)
print(f"Alice can write to repo: {can_write}")
```

**With Context Facts:**
```python
# Check authorization with temporary context
can_read_public = oso.authorize(
    alice, 
    "read", 
    repo, 
    [("is_public", repo)]
)
```

</Tab>
<Tab title="Go">

```go
osoClient.Authorize(actor Value, action string, resource Value) (bool, error)
osoClient.AuthorizeWithContextFacts(actor Value, action string, resource Value, contextFacts []Fact) (bool, error)
```

**Basic Example:**
```go
import oso "github.com/osohq/go-oso-cloud/v2"

alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Check if Alice can read the repository
canRead, err := osoClient.Authorize(alice, "read", repo)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Alice can read repo: %t\n", canRead)

// Check if Alice can write to the repository
canWrite, err := osoClient.Authorize(alice, "write", repo)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Alice can write to repo: %t\n", canWrite)
```

**With Context Facts:**
```go
// Check authorization with temporary context
contextFacts := []oso.Fact{oso.NewFact("is_public", repo)}
canReadPublic, err := osoClient.AuthorizeWithContextFacts(alice, "read", repo, contextFacts)
```

</Tab>
<Tab title="Java">

```java
oso.authorize(actor: Value, action: String, resource: Value, contextFacts: Fact[]): boolean throws ApiException
```

**Basic Example:**
```java
import com.osohq.oso_cloud.*;

Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
boolean canRead = oso.authorize(alice, "read", repo);
System.out.println("Alice can read repo: " + canRead);

// Check if Alice can write to the repository
boolean canWrite = oso.authorize(alice, "write", repo);
System.out.println("Alice can write to repo: " + canWrite);
```

**With Context Facts:**
```java
// Check authorization with temporary context
Fact[] contextFacts = {new Fact("is_public", repo)};
boolean canReadPublic = oso.authorize(alice, "read", repo, contextFacts);
```

</Tab>
<Tab title="Ruby">

```ruby
oso.authorize(actor, action, resource, context_facts = nil)
```

**Basic Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Check if Alice can read the repository
can_read = oso.authorize(alice, "read", repo)
puts "Alice can read repo: #{can_read}"

# Check if Alice can write to the repository
can_write = oso.authorize(alice, "write", repo)
puts "Alice can write to repo: #{can_write}"
```

**With Context Facts:**
```ruby
# Check authorization with temporary context
can_read_public = oso.authorize(alice, "read", repo, [["is_public", repo]])
```

</Tab>
<Tab title=".NET">

```csharp
oso.Authorize(actor: Value, action: string, resource: Value, contextFacts: Fact[] = null): bool
```

**Basic Example:**
```csharp
using OsoCloud;

var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
var canRead = oso.Authorize(alice, "read", repo);
Console.WriteLine($"Alice can read repo: {canRead}");

// Check if Alice can write to the repository
var canWrite = oso.Authorize(alice, "write", repo);
Console.WriteLine($"Alice can write to repo: {canWrite}");
```

**With Context Facts:**
```csharp
// Check authorization with temporary context
var contextFacts = new Fact[] { new Fact("is_public", repo) };
var canReadPublic = oso.Authorize(alice, "read", repo, contextFacts);
```

</Tab>
</Tabs>

## List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

<Tabs>
<Tab title="Node.js">

```javascript
oso.list(actor: Value, action: string, resourceType: string, contextFacts?: Fact[]): Promise<string[]>
```

**Example:**
```javascript
const alice = new Value("User", "alice");

// Get all repositories Alice can read
const readableRepos = await oso.list(alice, "read", "Repository");
console.log("Readable repositories:", readableRepos);

// Get all organizations Alice can admin
const adminOrgs = await oso.list(alice, "admin", "Organization");
console.log("Admin organizations:", adminOrgs);
```

**With Context Facts:**
```javascript
// List with additional context
const publicRepos = await oso.list(
  alice, 
  "read", 
  "Repository",
  [["user_preference", alice, "show_public", true]]
);
```

</Tab>
<Tab title="Python">

```python
oso.list(actor: Value, action: str, resource_type: str, context_facts: list[tuple] = None) -> List[str]
```

**Example:**
```python
alice = Value("User", "alice")

# Get all repositories Alice can read
readable_repos = oso.list(alice, "read", "Repository")
print("Readable repositories:", readable_repos)

# Get all organizations Alice can admin
admin_orgs = oso.list(alice, "admin", "Organization")
print("Admin organizations:", admin_orgs)
```

**With Context Facts:**
```python
# List with additional context
public_repos = oso.list(
    alice, 
    "read", 
    "Repository",
    [("user_preference", alice, "show_public", True)]
)
```

</Tab>
<Tab title="Go">

```go
osoClient.List(actor Value, action string, resourceType string, contextFacts []Fact) ([]string, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")

// Get all repositories Alice can read
readableRepos, err := osoClient.List(alice, "read", "Repository", nil)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Readable repositories: %v\n", readableRepos)

// Get all organizations Alice can admin
adminOrgs, err := osoClient.List(alice, "admin", "Organization", nil)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Admin organizations: %v\n", adminOrgs)
```

**With Context Facts:**
```go
// List with additional context
contextFacts := []oso.Fact{oso.NewFact("user_preference", alice, "show_public", true)}
publicRepos, err := osoClient.List(alice, "read", "Repository", contextFacts)
```

</Tab>
<Tab title="Java">

```java
oso.list(actor: Value, action: String, resourceType: String, contextFacts: Fact[]): String[] throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");

// Get all repositories Alice can read
String[] readableRepos = oso.list(alice, "read", "Repository");
System.out.println("Readable repositories: " + Arrays.toString(readableRepos));

// Get all organizations Alice can admin
String[] adminOrgs = oso.list(alice, "admin", "Organization");
System.out.println("Admin organizations: " + Arrays.toString(adminOrgs));
```

**With Context Facts:**
```java
// List with additional context
Fact[] contextFacts = {new Fact("user_preference", alice, new Value("show_public"), new Value(true))};
String[] publicRepos = oso.list(alice, "read", "Repository", contextFacts);
```

</Tab>
<Tab title="Ruby">

```ruby
oso.list(actor, action, resource_type, context_facts = nil)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }

# Get all repositories Alice can read
readable_repos = oso.list(alice, "read", "Repository")
puts "Readable repositories: #{readable_repos}"

# Get all organizations Alice can admin
admin_orgs = oso.list(alice, "admin", "Organization")
puts "Admin organizations: #{admin_orgs}"
```

**With Context Facts:**
```ruby
# List with additional context
public_repos = oso.list(alice, "read", "Repository", [["user_preference", alice, "show_public", true]])
```

</Tab>
<Tab title=".NET">

```csharp
oso.List(actor: Value, action: string, resourceType: string, contextFacts: Fact[] = null): List<string>
```

**Example:**
```csharp
var alice = new Value("User", "alice");

// Get all repositories Alice can read
var readableRepos = oso.List(alice, "read", "Repository");
Console.WriteLine($"Readable repositories: {string.Join(", ", readableRepos)}");

// Get all organizations Alice can admin
var adminOrgs = oso.List(alice, "admin", "Organization");
Console.WriteLine($"Admin organizations: {string.Join(", ", adminOrgs)}");
```

**With Context Facts:**
```csharp
// List with additional context
var contextFacts = new Fact[] { new Fact("user_preference", alice, new Value("show_public"), new Value(true)) };
var publicRepos = oso.List(alice, "read", "Repository", contextFacts);
```

</Tab>
</Tabs>

## List Actions

Fetches all actions that an actor can perform on a specific resource.

<Tabs>
<Tab title="Node.js">

```javascript
oso.actions(actor: Value, resource: Value, contextFacts?: Fact[]): Promise<string[]>
```

**Example:**
```javascript
const alice = new Value("User", "alice");
const repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
const allowedActions = await oso.actions(alice, repo);
console.log("Allowed actions:", allowedActions);
// Output: ["read", "write"] or ["read", "write", "admin"]
```

</Tab>
<Tab title="Python">

```python
oso.actions(actor: Value, resource: Value, context_facts: list[tuple] = None) -> List[str]
```

**Example:**
```python
alice = Value("User", "alice")
repo = Value("Repository", "acme/widgets")

# Get all actions Alice can perform on the repository
allowed_actions = oso.actions(alice, repo)
print("Allowed actions:", allowed_actions)
# Output: ["read", "write"] or ["read", "write", "admin"]
```

</Tab>
<Tab title="Go">

```go
osoClient.Actions(actor Value, resource Value) ([]string, error)
```

**Example:**
```go
alice := oso.NewValue("User", "alice")
repo := oso.NewValue("Repository", "acme/widgets")

// Get all actions Alice can perform on the repository
allowedActions, err := osoClient.Actions(alice, repo)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Allowed actions: %v\n", allowedActions)
// Output: ["read", "write"] or ["read", "write", "admin"]
```

</Tab>
<Tab title="Java">

```java
oso.actions(actor: Value, resource: Value, contextFacts: Fact[]): String[] throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
String[] allowedActions = oso.actions(alice, repo);
System.out.println("Allowed actions: " + Arrays.toString(allowedActions));
// Output: ["read", "write"] or ["read", "write", "admin"]
```

</Tab>
<Tab title="Ruby">

```ruby
oso.actions(actor, resource, context_facts = nil)
```

**Example:**
```ruby
alice = { type: "User", id: "alice" }
repo = { type: "Repository", id: "acme/widgets" }

# Get all actions Alice can perform on the repository
allowed_actions = oso.actions(alice, repo)
puts "Allowed actions: #{allowed_actions}"
# Output: ["read", "write"] or ["read", "write", "admin"]
```

</Tab>
<Tab title=".NET">

```csharp
oso.Actions(actor: Value, resource: Value, contextFacts: Fact[] = null): List<string>
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
var allowedActions = oso.Actions(alice, repo);
Console.WriteLine($"Allowed actions: {string.Join(", ", allowedActions)}");
// Output: ["read", "write"] or ["read", "write", "admin"]
```

</Tab>
</Tabs>

## Advanced Querying

For complex authorization queries, use the Query Builder API to construct sophisticated queries with multiple conditions.

<Tabs>
<Tab title="Node.js">

```javascript
oso.buildQuery(predicate: [string, ...Value[]]): QueryBuilder
```

**Basic Query:**
```javascript
// Find all users who can admin a specific repository
const adminUsers = await oso.buildQuery(["allow", oso.var("user"), "admin", repo])
  .evaluate(oso.var("user"));

console.log("Admin users:", adminUsers);
```

**Complex Query with Conditions:**
```javascript
// Find all repositories in the "acme" organization that Alice can read
const acmeOrg = new Value("Organization", "acme");
const readableAcmeRepos = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .and(["parent", oso.var("repo"), acmeOrg])
  .evaluate(oso.var("repo"));

console.log("Readable acme repositories:", readableAcmeRepos);
```

**Query with Context Facts:**
```javascript
// Query with temporary context
const contextQuery = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .withContextFacts([["is_public", oso.var("repo")]])
  .evaluate(oso.var("repo"));
```

**Migration from v1:**
```javascript
// Old (v1)
const results = await oso.query("allow", alice, "read", oso.var("repo"));

// New (v2)
const results = await oso.buildQuery(["allow", alice, "read", oso.var("repo")])
  .evaluate(oso.var("repo"));
```

</Tab>
<Tab title="Python">

```python
oso.build_query(predicate: tuple[str, ...]) -> QueryBuilder
```

**Basic Query:**
```python
# Find all users who can admin a specific repository
admin_users = oso.build_query(("allow", oso.var("user"), "admin", repo)).evaluate(oso.var("user"))

print("Admin users:", admin_users)
```

**Complex Query with Conditions:**
```python
# Find all repositories in the "acme" organization that Alice can read
acme_org = Value("Organization", "acme")
readable_acme_repos = (oso.build_query(("allow", alice, "read", oso.var("repo")))
                      .and_(("parent", oso.var("repo"), acme_org))
                      .evaluate(oso.var("repo")))

print("Readable acme repositories:", readable_acme_repos)
```

**Query with Context Facts:**
```python
# Query with temporary context
context_query = (oso.build_query(("allow", alice, "read", oso.var("repo")))
                 .with_context_facts([("is_public", oso.var("repo"))])
                 .evaluate(oso.var("repo")))
```

**Migration from v1:**
```python
# Old (v1)
results = oso.query("allow", alice, "read", oso.var("repo"))

# New (v2)
results = oso.build_query(("allow", alice, "read", oso.var("repo"))).evaluate(oso.var("repo"))
```

</Tab>
<Tab title="Go">

```go
osoClient.BuildQuery(queryFact QueryFact) QueryBuilder
```

**Basic Query:**
```go
// Find all users who can admin a specific repository
adminUsers, err := osoClient.BuildQuery(oso.NewQueryFact("allow", oso.Variable("user"), oso.String("admin"), repo)).EvaluateValues(oso.Variable("user"))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Admin users: %v\n", adminUsers)
```

**Complex Query with Conditions:**
```go
// Find all repositories in the "acme" organization that Alice can read
acmeOrg := oso.NewValue("Organization", "acme")
readableAcmeRepos, err := osoClient.BuildQuery(oso.NewQueryFact("allow", alice, oso.String("read"), oso.Variable("repo"))).And(oso.NewQueryFact("parent", oso.Variable("repo"), acmeOrg)).EvaluateValues(oso.Variable("repo"))
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Readable acme repositories: %v\n", readableAcmeRepos)
```

**Migration from v1:**
```go
// Old (v1)
results, err := osoClient.Query("allow", alice, "read", oso.Variable("repo"))

// New (v2)
results, err := osoClient.BuildQuery(oso.NewQueryFact("allow", alice, oso.String("read"), oso.Variable("repo"))).EvaluateValues(oso.Variable("repo"))
```

</Tab>
<Tab title="Java">

```java
oso.buildQuery(predicate: String, ...args: Value): QueryBuilder
```

**Basic Query:**
```java
// Find all users who can admin a specific repository
String[] adminUsers = oso.buildQuery("allow", oso.var("user"), "admin", repo)
                        .evaluate(EvaluateArgs.values(oso.var("user")));

System.out.println("Admin users: " + Arrays.toString(adminUsers));
```

**Complex Query with Conditions:**
```java
// Find all repositories in the "acme" organization that Alice can read
Value acmeOrg = new Value("Organization", "acme");
String[] readableAcmeRepos = oso.buildQuery("allow", alice, "read", oso.var("repo"))
                               .and("parent", oso.var("repo"), acmeOrg)
                               .evaluate(EvaluateArgs.values(oso.var("repo")));

System.out.println("Readable acme repositories: " + Arrays.toString(readableAcmeRepos));
```

**Advanced Evaluation Options:**
```java
// Evaluate to get detailed results with nested maps
Map<String, Object>[] detailedResults = oso.buildQuery("allow", oso.var("user"), "read", oso.var("repo"))
                                          .evaluate(EvaluateArgs.nested(
                                              Map.of("user", oso.var("user"), "repo", oso.var("repo"))
                                          ));
```

**Migration from v0:**
```java
// Old (v0)
QueryResult[] results = oso.query("allow", alice, "read", oso.var("repo"));

// New (v1)
String[] results = oso.buildQuery("allow", alice, "read", oso.var("repo"))
                     .evaluate(EvaluateArgs.values(oso.var("repo")));
```

</Tab>
<Tab title="Ruby">

```ruby
oso.query(rule)
```

**Basic Query:**
```ruby
# Find all users who can admin a specific repository
admin_users = oso.query(["allow", { "type" => "User" }, "admin", repo])

puts "Admin users: #{admin_users}"
```

**Complex Query with Type Constraints:**
```ruby
# Find all repositories in the "acme" organization that Alice can read
readable_acme_repos = oso.query([
  "allow", 
  alice, 
  "read", 
  { "type" => "Repository", "parent" => { "type" => "Organization", "id" => "acme" } }
])

puts "Readable acme repositories: #{readable_acme_repos}"
```

**Note:** Ruby uses a simple query interface without the QueryBuilder pattern.

</Tab>
<Tab title=".NET">

```csharp
oso.Query(rule: QueryFact): QueryResult[]
```

**Basic Query:**
```csharp
// Find all users who can admin a specific repository
var adminUsers = oso.Query(new QueryFact("allow", new Variable("User"), "admin", repo));

Console.WriteLine($"Admin users: {string.Join(", ", adminUsers)}");
```

**Complex Query with Type Constraints:**
```csharp
// Find all repositories that Alice can read
var readableRepos = oso.Query(new QueryFact("allow", alice, "read", new Variable("Repository")));

Console.WriteLine($"Readable repositories: {string.Join(", ", readableRepos)}");
```

**Note:** .NET uses a simple query interface without the QueryBuilder pattern.

</Tab>
</Tabs>

## Context Facts

Context facts are temporary facts that apply only to a specific authorization check. They're useful for:

- **Request-specific data**: User's current location, time of day
- **Dynamic conditions**: Resource state that changes frequently  
- **What-if scenarios**: Testing authorization under different conditions

### Example Use Cases

```javascript
// Check if user can access resource during business hours
const canAccessDuringBusinessHours = await oso.authorize(
  user, 
  "access", 
  resource,
  [["current_time", new Date().getHours()], ["business_hours", true]]
);

// Check if user can access resource from their current location
const canAccessFromLocation = await oso.authorize(
  user,
  "access", 
  resource,
  [["user_location", user, "office"], ["resource_requires_office_access", resource]]
);
```

## Performance Considerations

### Caching

- **Authorization results**: Cache frequently-checked permissions
- **Resource lists**: Cache resource lists for common actors
- **Context facts**: Avoid expensive context fact computation

### Batch Operations

```javascript
// Instead of multiple individual checks
const canRead = await oso.authorize(alice, "read", repo1);
const canWrite = await oso.authorize(alice, "write", repo1);
const canAdmin = await oso.authorize(alice, "admin", repo1);

// Use actions() for multiple permission checks on same resource
const allowedActions = await oso.actions(alice, repo1);
const canRead = allowedActions.includes("read");
const canWrite = allowedActions.includes("write");
const canAdmin = allowedActions.includes("admin");
```

### Query Optimization

- **Specific queries**: Use specific values instead of broad wildcards
- **Early filtering**: Apply most selective conditions first
- **Resource type filtering**: Use typed queries when possible

## Error Handling

```javascript
try {
  const allowed = await oso.authorize(user, action, resource);
  return allowed;
} catch (error) {
  if (error.code === 'POLICY_NOT_FOUND') {
    console.log('No policy deployed');
    return false; // Fail closed
  } else if (error.code === 'INVALID_ACTOR') {
    console.log('Invalid user format');
    throw new Error('Authentication required');
  } else {
    console.error('Authorization check failed:', error);
    throw error; // Re-throw unexpected errors
  }
}
```

## HTTP API Reference

These client library methods correspond to the following HTTP API endpoints:

- [Authorize](/reference/api/check-api/post-authorize): `POST /authorize`
- [List Resources](/reference/api/check-api/post-list): `POST /list`
- [List Actions](/reference/api/check-api/post-actions): `POST /actions`
- [Query](/reference/api/check-api/post-query): `POST /query`

## Next Steps

<CardGroup cols={2}>
  <Card
    title="Local Authorization"
    icon="zap"
    href="/reference/client-libraries/local-authorization"
  >
    Optimize performance with database-level filtering
  </Card>
  
  <Card
    title="Policy Patterns"
    icon="puzzle-piece"
    href="/develop/policies/patterns"
  >
    Learn common authorization patterns
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/facts"
  >
    Manage the data that drives authorization decisions
  </Card>
  
  <Card
    title="Performance Guide"
    icon="gauge-high"
    href="/develop/troubleshooting/query-performance"
  >
    Optimize authorization performance
  </Card>
</CardGroup>
