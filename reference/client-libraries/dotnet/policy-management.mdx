---
title: Policy Management
description: "Deploy and manage Polar authorization policies in Oso Cloud."
sidebarTitle: "Policy Management"
---

Deploy and manage your Polar authorization policies in Oso Cloud.

## Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```csharp
oso.Policy(policy: string): void
```

**Example:**
```csharp
try
{
    var policy = @"
        actor User {}
        resource Repository {
            permissions = [""read"", ""write"", ""admin""];
            roles = [""member"", ""maintainer"", ""admin""];
            
            ""member"" if ""maintainer"";
            ""maintainer"" if ""admin"";
        }
        
        allow(user: User, ""read"", repo: Repository) if
            has_role(user, ""member"", repo);
            
        allow(user: User, ""write"", repo: Repository) if
            has_role(user, ""maintainer"", repo);
            
        allow(user: User, ""admin"", repo: Repository) if
            has_role(user, ""admin"", repo);
            
        test ""basic permissions"" {
            setup {
                has_role(User{""alice""}, ""member"", Repository{""widgets""});
            }
            
            assert allow(User{""alice""}, ""read"", Repository{""widgets""});
            assert_not allow(User{""alice""}, ""write"", Repository{""widgets""});
        }
    ";
    
    oso.Policy(policy);
    Console.WriteLine("Policy deployed successfully!");
}
catch (Exception ex)
{
    Console.WriteLine($"Policy deployment failed: {ex.Message}");
}
```

## Get Policy

Retrieve the currently deployed policy.

```csharp
oso.GetPolicy(): string
```

**Example:**
```csharp
var currentPolicy = oso.GetPolicy();
Console.WriteLine($"Current policy: {currentPolicy}");
```

## Policy Stats

Get statistics about your deployed policy.

```csharp
oso.Stats(): PolicyStats
```

**Example:**
```csharp
var stats = oso.Stats();
Console.WriteLine($"Policy statistics: NumRules={stats.NumRules}, NumTypes={stats.NumTypes}, NumResources={stats.NumResources}");
```

## Next Steps

- **[Local authorization](/reference/client-libraries/dotnet/local-authorization)** - Database-level filtering for performance
- **[Error handling](/reference/client-libraries/dotnet/error-handling)** - Handle policy deployment failures
