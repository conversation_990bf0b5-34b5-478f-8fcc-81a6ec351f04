---
title: Error Handling
description: "Exception handling and error recovery patterns for robust .NET applications."
sidebarTitle: "Error Handling"
---

## Exception Types

```csharp
using OsoCloud.Exceptions;

public async Task<bool> CheckAuthorizationAsync(Value user, string action, Value resource)
{
    try
    {
        return oso.Authorize(user, action, resource);
    }
    catch (PolicyNotFoundException)
    {
        Console.WriteLine("No policy deployed");
        return false; // Fail closed
    }
    catch (InvalidActorException)
    {
        Console.WriteLine("Invalid user format");
        throw new ArgumentException("Authentication required");
    }
    catch (OsoException ex)
    {
        Console.WriteLine($"Authorization check failed: {ex.Message}");
        throw; // Re-throw unexpected errors
    }
}
```

## Logging and Monitoring

```csharp
public class AuthorizationService
{
    private readonly IOsoClient _oso;
    private readonly ILogger<AuthorizationService> _logger;
    
    public AuthorizationService(IOsoClient oso, ILogger<AuthorizationService> logger)
    {
        _oso = oso;
        _logger = logger;
    }
    
    public bool Authorize(Value user, string action, Value resource)
    {
        try
        {
            var result = _oso.Authorize(user, action, resource);
            _logger.LogDebug("Authorization check: {User} {Action} {Resource} = {Result}", 
                user.Id, action, resource.Id, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authorization failed for {User} {Action} {Resource}", 
                user.Id, action, resource.Id);
            throw;
        }
    }
}
```

## Next Steps

- **[Performance optimization](/reference/client-libraries/dotnet/performance)** - Improve authorization performance
- **[Testing strategies](/reference/client-libraries/dotnet/testing)** - Test error scenarios
