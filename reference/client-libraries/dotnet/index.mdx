---
title: .NET Client Library
description: "Complete guide to using the Oso Cloud .NET client library for authorization."
sidebarTitle: ".NET"
---

The Oso Cloud .NET client library provides a comprehensive solution for implementing authorization in .NET applications. Built with modern .NET practices, it offers strong typing, dependency injection support, and async/await patterns.

## Quick Start

Get started with Oso Cloud in your .NET application:

```csharp
using OsoCloud;

var oso = new OsoClient(new OsoConfig
{
    Url = "https://cloud.osohq.com",
    ApiKey = Environment.GetEnvironmentVariable("OSO_AUTH")
});

var user = new Value("User", "alice");
var resource = new Value("Repository", "acme/widgets");

// Check authorization
var allowed = oso.Authorize(user, "read", resource);
Console.WriteLine($"Access allowed: {allowed}");
```

## What's Included

<CardGroup cols={2}>
  <Card
    title="Installation & Setup"
    icon="download"
    href="/reference/client-libraries/dotnet/installation"
  >
    Install the NuGet package and configure your .NET application
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/dotnet/authorization-checks"
  >
    Core authorization methods: authorize, list, actions, and querying
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/dotnet/facts-management"
  >
    CRUD operations for authorization data with batch support
  </Card>
  
  <Card
    title="Policy Management"
    icon="file-code"
    href="/reference/client-libraries/dotnet/policy-management"
  >
    Deploy and manage Polar authorization policies
  </Card>
  
  <Card
    title="Local Authorization"
    icon="server"
    href="/reference/client-libraries/dotnet/local-authorization"
  >
    Database-level filtering with Entity Framework integration
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/dotnet/migration-guide"
  >
    Stable API with no breaking changes
  </Card>
  
  <Card
    title="Error Handling"
    icon="exclamation-triangle"
    href="/reference/client-libraries/dotnet/error-handling"
  >
    Exception handling and error recovery patterns
  </Card>
  
  <Card
    title="Performance"
    icon="gauge-high"
    href="/reference/client-libraries/dotnet/performance"
  >
    Optimization tips, caching, and async patterns
  </Card>
  
  <Card
    title="Testing"
    icon="flask"
    href="/reference/client-libraries/dotnet/testing"
  >
    Unit testing with Moq and integration testing patterns
  </Card>
</CardGroup>

## Key Features

### Strong Typing
Full support for .NET type system with compile-time safety:

```csharp
var user = new Value("User", userId);
var resource = new Value("Repository", repositoryId);
var allowed = oso.Authorize(user, "read", resource);
```

### Dependency Injection
Native ASP.NET Core dependency injection support:

```csharp
services.AddSingleton<IOsoClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    return new OsoClient(new OsoConfig
    {
        Url = "https://cloud.osohq.com",
        ApiKey = configuration["Oso:ApiKey"]
    });
});
```

### Async/Await Support
Asynchronous operations for better scalability:

```csharp
public async Task<bool> AuthorizeAsync(Value user, string action, Value resource)
{
    return await Task.Run(() => oso.Authorize(user, action, resource));
}
```

### Entity Framework Integration
Seamless integration with Entity Framework for local authorization:

```csharp
var filter = oso.ListLocal(user, "read", "Repository", "Id");
var repositories = await _context.Repositories
    .FromSqlRaw($"SELECT * FROM Repositories WHERE {filter}")
    .ToListAsync();
```

## Framework Support

- **.NET 6.0+** - Full support for modern .NET
- **ASP.NET Core** - Built-in dependency injection and middleware
- **Entity Framework Core** - Database integration for local authorization
- **Blazor** - Client-side and server-side Blazor applications
- **MAUI** - Cross-platform mobile and desktop applications

## Common Use Cases

### Web API Authorization
```csharp
[ApiController]
[Route("api/[controller]")]
public class RepositoriesController : ControllerBase
{
    private readonly IAuthorizationService _authService;
    
    [HttpGet("{id}")]
    public async Task<IActionResult> GetRepository(string id)
    {
        var repository = await _repositoryService.GetByIdAsync(id);
        
        if (!_authService.Authorize(User, "read", repository))
        {
            return Forbid();
        }
        
        return Ok(repository);
    }
}
```

### Blazor Component Authorization
```csharp
@inject IAuthorizationService AuthService

@if (AuthService.Authorize(CurrentUser, "admin", Repository))
{
    <button @onclick="DeleteRepository">Delete</button>
}
```

### Background Service Authorization
```csharp
public class ProcessingService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var user = new Value("System", "background-processor");
        
        if (oso.Authorize(user, "process", resource))
        {
            await ProcessResourceAsync(resource);
        }
    }
}
```

## Next Steps

1. **[Install the package](/reference/client-libraries/dotnet/installation)** - Get up and running in minutes
2. **[Learn authorization basics](/reference/client-libraries/dotnet/authorization-checks)** - Master the core concepts
3. **[Explore examples](https://github.com/osohq/oso-cloud-dotnet-examples)** - See real-world implementations

## Support

- **Documentation**: Complete API reference and guides
- **Examples**: Sample applications and integration patterns  
- **Community**: GitHub discussions and Stack Overflow
- **Enterprise**: Priority support and custom integrations
