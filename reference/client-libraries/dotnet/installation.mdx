---
title: Installation & Setup
description: "Install and configure the Oso Cloud .NET client library in your application."
sidebarTitle: "Installation"
---

## Installation

Install the Oso Cloud .NET package:

```bash
dotnet add package OsoCloud
```

**Requirements:**
- .NET 6.0 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```csharp
using OsoCloud;

var oso = new OsoClient(new OsoConfig
{
    Url = "https://cloud.osohq.com",
    ApiKey = Environment.GetEnvironmentVariable("OSO_AUTH")
});
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```csharp
var oso = new OsoClient(new OsoConfig
{
    Url = "https://cloud.osohq.com",
    ApiKey = Environment.GetEnvironmentVariable("OSO_AUTH"),
    Timeout = TimeSpan.FromSeconds(30),  // Request timeout
    Retries = 3                          // Number of retry attempts
});
```

## ASP.NET Core Integration

### Dependency Injection Setup

Configure Oso Cloud in your `Program.cs` or `Startup.cs`:

```csharp
// Program.cs (.NET 6+)
using OsoCloud;

var builder = WebApplication.CreateBuilder(args);

// Register Oso Cloud client
builder.Services.AddSingleton<IOsoClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    return new OsoClient(new OsoConfig
    {
        Url = "https://cloud.osohq.com",
        ApiKey = configuration["Oso:ApiKey"]
    });
});

// Register authorization service
builder.Services.AddScoped<IAuthorizationService, AuthorizationService>();

var app = builder.Build();
```

### Configuration

Add Oso configuration to your `appsettings.json`:

```json
{
  "Oso": {
    "ApiKey": "your-api-key-here",
    "Url": "https://cloud.osohq.com",
    "Timeout": "00:00:30",
    "Retries": 3
  }
}
```

For production, use user secrets or environment variables:

```bash
# Using user secrets (development)
dotnet user-secrets set "Oso:ApiKey" "your-api-key-here"

# Using environment variables (production)
export Oso__ApiKey="your-api-key-here"
```

### Authorization Service

Create a service to encapsulate authorization logic:

```csharp
public interface IAuthorizationService
{
    bool Authorize(ClaimsPrincipal user, string action, object resource);
    Task<bool> AuthorizeAsync(ClaimsPrincipal user, string action, object resource);
    List<string> ListResources(ClaimsPrincipal user, string action, string resourceType);
    List<string> ListActions(ClaimsPrincipal user, object resource);
}

public class AuthorizationService : IAuthorizationService
{
    private readonly IOsoClient _oso;
    
    public AuthorizationService(IOsoClient oso)
    {
        _oso = oso;
    }
    
    public bool Authorize(ClaimsPrincipal user, string action, object resource)
    {
        var actor = CreateActor(user);
        var resourceValue = CreateResource(resource);
        
        return _oso.Authorize(actor, action, resourceValue);
    }
    
    public async Task<bool> AuthorizeAsync(ClaimsPrincipal user, string action, object resource)
    {
        return await Task.Run(() => Authorize(user, action, resource));
    }
    
    public List<string> ListResources(ClaimsPrincipal user, string action, string resourceType)
    {
        var actor = CreateActor(user);
        return _oso.List(actor, action, resourceType);
    }
    
    public List<string> ListActions(ClaimsPrincipal user, object resource)
    {
        var actor = CreateActor(user);
        var resourceValue = CreateResource(resource);
        return _oso.Actions(actor, resourceValue);
    }
    
    private Value CreateActor(ClaimsPrincipal user)
    {
        var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return new Value("User", userId);
    }
    
    private Value CreateResource(object resource)
    {
        var resourceType = resource.GetType().Name;
        var resourceId = GetResourceId(resource);
        return new Value(resourceType, resourceId);
    }
    
    private string GetResourceId(object resource)
    {
        var idProperty = resource.GetType().GetProperty("Id");
        return idProperty?.GetValue(resource)?.ToString();
    }
}
```

## Entity Framework Integration

### DbContext Configuration

Configure your DbContext to work with local authorization:

```csharp
public class ApplicationDbContext : DbContext
{
    private readonly IOsoClient _oso;
    private readonly IHttpContextAccessor _httpContextAccessor;
    
    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        IOsoClient oso,
        IHttpContextAccessor httpContextAccessor) : base(options)
    {
        _oso = oso;
        _httpContextAccessor = httpContextAccessor;
    }
    
    public DbSet<Repository> Repositories { get; set; }
    public DbSet<Organization> Organizations { get; set; }
    
    // Helper method for authorized queries
    public IQueryable<T> AuthorizedQuery<T>(string action) where T : class
    {
        var user = GetCurrentUser();
        var resourceType = typeof(T).Name;
        var filter = _oso.ListLocal(user, action, resourceType, "Id");
        
        return Set<T>().FromSqlRaw($"SELECT * FROM {typeof(T).Name}s WHERE {filter}");
    }
    
    private Value GetCurrentUser()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var userId = httpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return new Value("User", userId);
    }
}
```

### Repository Pattern

Implement the repository pattern with authorization:

```csharp
public interface IRepositoryService
{
    Task<List<Repository>> GetReadableRepositoriesAsync();
    Task<Repository> GetRepositoryAsync(string id);
    Task<bool> CanUserAccessRepositoryAsync(string userId, string repositoryId, string action);
}

public class RepositoryService : IRepositoryService
{
    private readonly ApplicationDbContext _context;
    private readonly IOsoClient _oso;
    
    public RepositoryService(ApplicationDbContext context, IOsoClient oso)
    {
        _context = context;
        _oso = oso;
    }
    
    public async Task<List<Repository>> GetReadableRepositoriesAsync()
    {
        return await _context.AuthorizedQuery<Repository>("read").ToListAsync();
    }
    
    public async Task<Repository> GetRepositoryAsync(string id)
    {
        var user = GetCurrentUser();
        var condition = _oso.AuthorizeLocal(user, "read", new Value("Repository", id), "Id");
        
        return await _context.Repositories
            .FromSqlRaw($"SELECT * FROM Repositories WHERE {condition}")
            .FirstOrDefaultAsync();
    }
    
    public async Task<bool> CanUserAccessRepositoryAsync(string userId, string repositoryId, string action)
    {
        var user = new Value("User", userId);
        var resource = new Value("Repository", repositoryId);
        
        return _oso.Authorize(user, action, resource);
    }
    
    private Value GetCurrentUser()
    {
        // Implementation depends on your authentication setup
        throw new NotImplementedException();
    }
}
```

## Blazor Integration

### Server-Side Blazor

Configure Oso in your Blazor Server application:

```csharp
// Program.cs
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

// Add Oso Cloud
builder.Services.AddSingleton<IOsoClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    return new OsoClient(new OsoConfig
    {
        Url = "https://cloud.osohq.com",
        ApiKey = configuration["Oso:ApiKey"]
    });
});

builder.Services.AddScoped<IAuthorizationService, AuthorizationService>();
```

### Component Authorization

Use authorization in Blazor components:

```razor
@page "/repositories"
@inject IAuthorizationService AuthService
@inject IJSRuntime JSRuntime

<h3>Repositories</h3>

@if (repositories != null)
{
    @foreach (var repo in repositories)
    {
        <div class="card">
            <h5>@repo.Name</h5>
            
            @if (AuthService.Authorize(CurrentUser, "write", repo))
            {
                <button @onclick="() => EditRepository(repo)">Edit</button>
            }
            
            @if (AuthService.Authorize(CurrentUser, "admin", repo))
            {
                <button @onclick="() => DeleteRepository(repo)" class="btn-danger">Delete</button>
            }
        </div>
    }
}

@code {
    private List<Repository> repositories;
    
    protected override async Task OnInitializedAsync()
    {
        // Load only repositories the user can read
        repositories = await RepositoryService.GetReadableRepositoriesAsync();
    }
    
    private async Task EditRepository(Repository repo)
    {
        // Navigate to edit page
    }
    
    private async Task DeleteRepository(Repository repo)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", $"Delete {repo.Name}?"))
        {
            await RepositoryService.DeleteAsync(repo.Id);
            repositories.Remove(repo);
        }
    }
}
```

## Console Application

For console applications or background services:

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using OsoCloud;

var host = Host.CreateDefaultBuilder(args)
    .ConfigureServices((context, services) =>
    {
        services.AddSingleton<IOsoClient>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            return new OsoClient(new OsoConfig
            {
                Url = "https://cloud.osohq.com",
                ApiKey = configuration["Oso:ApiKey"]
            });
        });
        
        services.AddScoped<IAuthorizationService, AuthorizationService>();
        services.AddHostedService<ProcessingService>();
    })
    .Build();

await host.RunAsync();

public class ProcessingService : BackgroundService
{
    private readonly IOsoClient _oso;
    
    public ProcessingService(IOsoClient oso)
    {
        _oso = oso;
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var systemUser = new Value("System", "background-processor");
            var resource = new Value("Task", "daily-report");
            
            if (_oso.Authorize(systemUser, "execute", resource))
            {
                await ProcessDailyReportAsync();
            }
            
            await Task.Delay(TimeSpan.FromHours(24), stoppingToken);
        }
    }
    
    private async Task ProcessDailyReportAsync()
    {
        // Process daily report
    }
}
```

## Next Steps

- **[Learn authorization basics](/reference/client-libraries/dotnet/authorization-checks)** - Master the core authorization methods
- **[Manage facts](/reference/client-libraries/dotnet/facts-management)** - Store and retrieve authorization data
- **[Deploy policies](/reference/client-libraries/dotnet/policy-management)** - Manage your authorization policies
