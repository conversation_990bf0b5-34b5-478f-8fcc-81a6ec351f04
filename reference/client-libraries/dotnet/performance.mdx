---
title: Performance Optimization
description: "Caching, batching, and async patterns for high-performance .NET applications."
sidebarTitle: "Performance"
---

## HTTP Client Configuration

```csharp
// Configure HttpClient for optimal performance
services.AddHttpClient<IOsoClient, OsoClient>(client =>
{
    client.BaseAddress = new Uri("https://cloud.osohq.com");
    client.Timeout = TimeSpan.FromSeconds(30);
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
{
    MaxConnectionsPerServer = 10
});
```

## Batch Operations

```csharp
// Instead of multiple individual checks
var canRead = oso.Authorize(alice, "read", repo1);
var canWrite = oso.Authorize(alice, "write", repo1);
var canAdmin = oso.Authorize(alice, "admin", repo1);

// Use Actions() for multiple permission checks on same resource
var allowedActions = oso.Actions(alice, repo1);
var canRead = allowedActions.Contains("read");
var canWrite = allowedActions.Contains("write");
var canAdmin = allowedActions.Contains("admin");
```

## Caching

```csharp
public class CachedAuthorizationService
{
    private readonly IOsoClient _oso;
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);
    
    public CachedAuthorizationService(IOsoClient oso, IMemoryCache cache)
    {
        _oso = oso;
        _cache = cache;
    }
    
    public bool Authorize(Value user, string action, Value resource)
    {
        var key = $"auth:{user.Id}:{action}:{resource.Id}";
        
        if (_cache.TryGetValue(key, out bool cached))
        {
            return cached;
        }
        
        var result = _oso.Authorize(user, action, resource);
        _cache.Set(key, result, _cacheExpiry);
        return result;
    }
}
```

## Async Operations

```csharp
// Use async versions for better scalability
public async Task<bool> AuthorizeAsync(Value user, string action, Value resource)
{
    return await Task.Run(() => oso.Authorize(user, action, resource));
}

// Parallel authorization checks
var tasks = resources.Select(async resource => 
    new { Resource = resource, Allowed = await AuthorizeAsync(user, "read", resource) });

var results = await Task.WhenAll(tasks);
var allowedResources = results.Where(r => r.Allowed).Select(r => r.Resource);
```

## Next Steps

- **[Testing strategies](/reference/client-libraries/dotnet/testing)** - Test performance optimizations
- **[Local authorization](/reference/client-libraries/dotnet/local-authorization)** - Database-level filtering
