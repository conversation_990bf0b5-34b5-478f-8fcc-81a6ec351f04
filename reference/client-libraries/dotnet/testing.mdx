---
title: Testing
description: "Unit testing with Moq and integration testing patterns for .NET applications."
sidebarTitle: "Testing"
---

## Unit Testing with <PERSON>q

```csharp
using Moq;
using Xunit;

public class AuthorizationServiceTests
{
    private readonly Mock<IOsoClient> _mockOso;
    private readonly AuthorizationService _service;
    
    public AuthorizationServiceTests()
    {
        _mockOso = new Mock<IOsoClient>();
        _service = new AuthorizationService(_mockOso.Object);
    }
    
    [Fact]
    public void Authorize_WhenAllowed_ReturnsTrue()
    {
        // Arrange
        var user = new Value("User", "alice");
        var resource = new Value("Repository", "repo1");
        
        _mockOso.Setup(x => x.Authorize(user, "read", resource))
               .Returns(true);
        
        // Act
        var result = _service.Authorize(user, "read", resource);
        
        // Assert
        Assert.True(result);
        _mockOso.Verify(x => x.Authorize(user, "read", resource), Times.Once);
    }
    
    [Fact]
    public void Authorize_WhenNotAllowed_ReturnsFalse()
    {
        // Arrange
        var user = new Value("User", "alice");
        var resource = new Value("Repository", "repo1");
        
        _mockOso.Setup(x => x.Authorize(user, "read", resource))
               .Returns(false);
        
        // Act
        var result = _service.Authorize(user, "read", resource);
        
        // Assert
        Assert.False(result);
    }
}
```

## Integration Testing

```csharp
public class AuthorizationIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    
    public AuthorizationIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }
    
    [Fact]
    public async Task GetRepository_WhenAuthorized_ReturnsOk()
    {
        // Arrange
        var repositoryId = "test-repo";
        
        // Act
        var response = await _client.GetAsync($"/api/repositories/{repositoryId}");
        
        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
    
    [Fact]
    public async Task GetRepository_WhenNotAuthorized_ReturnsForbidden()
    {
        // Arrange
        var repositoryId = "private-repo";
        
        // Act
        var response = await _client.GetAsync($"/api/repositories/{repositoryId}");
        
        // Assert
        Assert.Equal(HttpStatusCode.Forbidden, response.StatusCode);
    }
}
```

## Test Helpers

```csharp
public static class TestHelpers
{
    public static Mock<IOsoClient> CreateMockOsoClient()
    {
        var mock = new Mock<IOsoClient>();
        
        // Default setup for common scenarios
        mock.Setup(x => x.Authorize(It.IsAny<Value>(), It.IsAny<string>(), It.IsAny<Value>()))
            .Returns(true);
            
        return mock;
    }
    
    public static Value CreateTestUser(string id = "test-user")
    {
        return new Value("User", id);
    }
    
    public static Value CreateTestRepository(string id = "test-repo")
    {
        return new Value("Repository", id);
    }
}
```

## Next Steps

- **[Authorization checks](/reference/client-libraries/dotnet/authorization-checks)** - Learn the core authorization methods
- **[Facts management](/reference/client-libraries/dotnet/facts-management)** - Test fact operations
