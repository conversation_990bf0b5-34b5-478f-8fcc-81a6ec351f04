---
title: Authorization Checks
description: "Core authorization methods for checking permissions, listing resources, and querying access patterns."
sidebarTitle: "Authorization Checks"
---

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

## Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```csharp
oso.Authorize(actor: Value, action: string, resource: Value, contextFacts: Fact[] = null): bool
```

**Basic Example:**
```csharp
using OsoCloud;

var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
var canRead = oso.Authorize(alice, "read", repo);
Console.WriteLine($"Alice can read repo: {canRead}");

// Check if Alice can write to the repository
var canWrite = oso.Authorize(alice, "write", repo);
Console.WriteLine($"Alice can write to repo: {canWrite}");
```

**With Context Facts:**
```csharp
// Check authorization with temporary context
var contextFacts = new Fact[] { new Fact("is_public", repo) };
var canReadPublic = oso.Authorize(alice, "read", repo, contextFacts);
```

### Real-World Examples

**Controller Authorization:**
```csharp
[ApiController]
[Route("api/[controller]")]
public class RepositoriesController : ControllerBase
{
    private readonly IOsoClient _oso;
    
    public RepositoriesController(IOsoClient oso)
    {
        _oso = oso;
    }
    
    [HttpGet("{id}")]
    public async Task<IActionResult> GetRepository(string id)
    {
        var user = new Value("User", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var repository = new Value("Repository", id);
        
        if (!_oso.Authorize(user, "read", repository))
        {
            return Forbid("You don't have permission to read this repository");
        }
        
        var repo = await _repositoryService.GetByIdAsync(id);
        return Ok(repo);
    }
    
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateRepository(string id, [FromBody] UpdateRepositoryRequest request)
    {
        var user = new Value("User", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var repository = new Value("Repository", id);
        
        if (!_oso.Authorize(user, "write", repository))
        {
            return Forbid("You don't have permission to modify this repository");
        }
        
        await _repositoryService.UpdateAsync(id, request);
        return NoContent();
    }
}
```

**Service Layer Authorization:**
```csharp
public class DocumentService
{
    private readonly IOsoClient _oso;
    private readonly IDocumentRepository _repository;
    
    public DocumentService(IOsoClient oso, IDocumentRepository repository)
    {
        _oso = oso;
        _repository = repository;
    }
    
    public async Task<Document> GetDocumentAsync(string userId, string documentId)
    {
        var user = new Value("User", userId);
        var document = new Value("Document", documentId);
        
        if (!_oso.Authorize(user, "read", document))
        {
            throw new UnauthorizedAccessException("Access denied to document");
        }
        
        return await _repository.GetByIdAsync(documentId);
    }
    
    public async Task ShareDocumentAsync(string userId, string documentId, string targetUserId)
    {
        var user = new Value("User", userId);
        var document = new Value("Document", documentId);
        
        // Check if user can share the document
        if (!_oso.Authorize(user, "share", document))
        {
            throw new UnauthorizedAccessException("You cannot share this document");
        }
        
        // Add sharing permission
        await _oso.Insert(new Fact("can_read", new Value("User", targetUserId), document));
    }
}
```

## List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```csharp
oso.List(actor: Value, action: string, resourceType: string, contextFacts: Fact[] = null): List<string>
```

**Example:**
```csharp
var alice = new Value("User", "alice");

// Get all repositories Alice can read
var readableRepos = oso.List(alice, "read", "Repository");
Console.WriteLine($"Readable repositories: {string.Join(", ", readableRepos)}");

// Get all organizations Alice can admin
var adminOrgs = oso.List(alice, "admin", "Organization");
Console.WriteLine($"Admin organizations: {string.Join(", ", adminOrgs)}");
```

**With Context Facts:**
```csharp
// List with additional context
var contextFacts = new Fact[] { new Fact("user_preference", alice, new Value("show_public"), new Value(true)) };
var publicRepos = oso.List(alice, "read", "Repository", contextFacts);
```

### Dashboard Implementation

```csharp
public class DashboardService
{
    private readonly IOsoClient _oso;
    private readonly IRepositoryService _repositoryService;
    private readonly IOrganizationService _organizationService;
    
    public DashboardService(
        IOsoClient oso, 
        IRepositoryService repositoryService,
        IOrganizationService organizationService)
    {
        _oso = oso;
        _repositoryService = repositoryService;
        _organizationService = organizationService;
    }
    
    public async Task<DashboardData> GetUserDashboardAsync(string userId)
    {
        var user = new Value("User", userId);
        
        // Get all resources the user can access
        var readableRepos = _oso.List(user, "read", "Repository");
        var writableRepos = _oso.List(user, "write", "Repository");
        var adminOrgs = _oso.List(user, "admin", "Organization");
        
        // Fetch actual data
        var repositories = await _repositoryService.GetByIdsAsync(readableRepos);
        var organizations = await _organizationService.GetByIdsAsync(adminOrgs);
        
        return new DashboardData
        {
            ReadableRepositories = repositories,
            WritableRepositoryIds = writableRepos,
            AdminOrganizations = organizations
        };
    }
}

public class DashboardData
{
    public List<Repository> ReadableRepositories { get; set; }
    public List<string> WritableRepositoryIds { get; set; }
    public List<Organization> AdminOrganizations { get; set; }
}
```

## List Actions

Fetches all actions that an actor can perform on a specific resource.

```csharp
oso.Actions(actor: Value, resource: Value, contextFacts: Fact[] = null): List<string>
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
var allowedActions = oso.Actions(alice, repo);
Console.WriteLine($"Allowed actions: {string.Join(", ", allowedActions)}");
// Output: ["read", "write"] or ["read", "write", "admin"]
```

### UI Permission Controls

```csharp
public class RepositoryViewModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public List<string> AllowedActions { get; set; }
    
    // Computed properties for UI
    public bool CanRead => AllowedActions.Contains("read");
    public bool CanWrite => AllowedActions.Contains("write");
    public bool CanAdmin => AllowedActions.Contains("admin");
    public bool CanDelete => AllowedActions.Contains("delete");
}

public class RepositoryController : Controller
{
    private readonly IOsoClient _oso;
    private readonly IRepositoryService _repositoryService;
    
    public async Task<IActionResult> Details(string id)
    {
        var user = new Value("User", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var repository = new Value("Repository", id);
        
        // Get allowed actions for this user and repository
        var allowedActions = _oso.Actions(user, repository);
        
        if (!allowedActions.Contains("read"))
        {
            return Forbid();
        }
        
        var repo = await _repositoryService.GetByIdAsync(id);
        var viewModel = new RepositoryViewModel
        {
            Id = repo.Id,
            Name = repo.Name,
            Description = repo.Description,
            AllowedActions = allowedActions
        };
        
        return View(viewModel);
    }
}
```

**Blazor Component:**
```razor
@inject IOsoClient Oso

<div class="repository-card">
    <h3>@Repository.Name</h3>
    <p>@Repository.Description</p>
    
    <div class="actions">
        @if (AllowedActions.Contains("write"))
        {
            <button class="btn btn-primary" @onclick="EditRepository">Edit</button>
        }
        
        @if (AllowedActions.Contains("admin"))
        {
            <button class="btn btn-secondary" @onclick="ManagePermissions">Permissions</button>
        }
        
        @if (AllowedActions.Contains("delete"))
        {
            <button class="btn btn-danger" @onclick="DeleteRepository">Delete</button>
        }
    </div>
</div>

@code {
    [Parameter] public Repository Repository { get; set; }
    [Parameter] public string UserId { get; set; }
    
    private List<string> AllowedActions { get; set; } = new();
    
    protected override async Task OnInitializedAsync()
    {
        var user = new Value("User", UserId);
        var resource = new Value("Repository", Repository.Id);
        
        AllowedActions = Oso.Actions(user, resource);
    }
    
    private async Task EditRepository()
    {
        // Navigate to edit page
    }
    
    private async Task ManagePermissions()
    {
        // Navigate to permissions page
    }
    
    private async Task DeleteRepository()
    {
        // Show confirmation and delete
    }
}
```

## Advanced Querying

For complex authorization queries, use the simple query interface.

```csharp
oso.Query(rule: QueryFact): QueryResult[]
```

**Basic Query:**
```csharp
// Find all users who can admin a specific repository
var adminUsers = oso.Query(new QueryFact("allow", new Variable("User"), "admin", repo));

Console.WriteLine($"Admin users: {string.Join(", ", adminUsers)}");
```

**Complex Query with Type Constraints:**
```csharp
// Find all repositories that Alice can read
var readableRepos = oso.Query(new QueryFact("allow", alice, "read", new Variable("Repository")));

Console.WriteLine($"Readable repositories: {string.Join(", ", readableRepos)}");
```

**Note:** .NET uses a simple query interface without the QueryBuilder pattern.

### Administrative Queries

```csharp
public class AdminService
{
    private readonly IOsoClient _oso;
    
    public AdminService(IOsoClient oso)
    {
        _oso = oso;
    }
    
    public List<string> GetRepositoryAdmins(string repositoryId)
    {
        var repository = new Value("Repository", repositoryId);
        var results = _oso.Query(new QueryFact("allow", new Variable("User"), "admin", repository));
        
        return results.Select(r => r.GetValue("User")).ToList();
    }
    
    public List<string> GetUserRepositories(string userId, string action = "read")
    {
        var user = new Value("User", userId);
        var results = _oso.Query(new QueryFact("allow", user, action, new Variable("Repository")));
        
        return results.Select(r => r.GetValue("Repository")).ToList();
    }
    
    public Dictionary<string, List<string>> GetUserPermissionMatrix(string userId)
    {
        var user = new Value("User", userId);
        var matrix = new Dictionary<string, List<string>>();
        
        var actions = new[] { "read", "write", "admin", "delete" };
        
        foreach (var action in actions)
        {
            var results = _oso.Query(new QueryFact("allow", user, action, new Variable("Repository")));
            matrix[action] = results.Select(r => r.GetValue("Repository")).ToList();
        }
        
        return matrix;
    }
}
```

## Context Facts

Context facts provide temporary, request-specific information for authorization decisions.

```csharp
// Time-based authorization
var contextFacts = new Fact[]
{
    new Fact("current_time", DateTime.Now.Hour),
    new Fact("business_hours", true)
};

var canAccessDuringBusinessHours = oso.Authorize(user, "access", resource, contextFacts);

// Location-based authorization
var locationFacts = new Fact[]
{
    new Fact("user_location", user, "office"),
    new Fact("resource_requires_office_access", resource)
};

var canAccessFromOffice = oso.Authorize(user, "access", resource, locationFacts);

// Feature flag authorization
var featureFacts = new Fact[]
{
    new Fact("feature_enabled", "advanced_analytics", true),
    new Fact("user_tier", user, "premium")
};

var canUseAdvancedFeatures = oso.Authorize(user, "use_advanced_analytics", resource, featureFacts);
```

## Performance Tips

### Batch Authorization Checks

```csharp
// Instead of multiple individual checks
var canRead = oso.Authorize(alice, "read", repo1);
var canWrite = oso.Authorize(alice, "write", repo1);
var canAdmin = oso.Authorize(alice, "admin", repo1);

// Use Actions() for multiple permission checks on same resource
var allowedActions = oso.Actions(alice, repo1);
var canRead = allowedActions.Contains("read");
var canWrite = allowedActions.Contains("write");
var canAdmin = allowedActions.Contains("admin");
```

### Caching Results

```csharp
public class CachedAuthorizationService
{
    private readonly IOsoClient _oso;
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);
    
    public CachedAuthorizationService(IOsoClient oso, IMemoryCache cache)
    {
        _oso = oso;
        _cache = cache;
    }
    
    public bool Authorize(Value user, string action, Value resource)
    {
        var key = $"auth:{user.Id}:{action}:{resource.Id}";
        
        if (_cache.TryGetValue(key, out bool cached))
        {
            return cached;
        }
        
        var result = _oso.Authorize(user, action, resource);
        _cache.Set(key, result, _cacheExpiry);
        return result;
    }
}
```

## Next Steps

- **[Manage facts](/reference/client-libraries/dotnet/facts-management)** - Store and retrieve authorization data
- **[Local authorization](/reference/client-libraries/dotnet/local-authorization)** - Database-level filtering for performance
- **[Error handling](/reference/client-libraries/dotnet/error-handling)** - Handle authorization failures gracefully
