---
title: Facts Management
description: "Store, retrieve, and manage authorization data with CRUD operations and batch processing."
sidebarTitle: "Facts Management"
---

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions. Common examples include role assignments, group memberships, and resource properties.

## Insert Facts

Adds a single fact to the centralized authorization data store.

```csharp
oso.Insert(fact: Fact): void
```

**Example:**
```csharp
using OsoCloud;

// Create entity values
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");
var org = new Value("Organization", "acme");

// Insert role assignment
oso.Insert(new Fact("has_role", alice, "admin", repo));

// Insert organization membership
oso.Insert(new Fact("member_of", alice, org));

// Insert resource property
oso.Insert(new Fact("is_public", repo));
```

### Real-World Examples

**User Registration:**
```csharp
public class UserService
{
    private readonly IOsoClient _oso;
    
    public UserService(IOsoClient oso)
    {
        _oso = oso;
    }
    
    public async Task RegisterUserAsync(string userId, string organizationId, string role = "member")
    {
        var user = new Value("User", userId);
        var organization = new Value("Organization", organizationId);
        
        // Add user to organization with default role
        _oso.Insert(new Fact("member_of", user, organization));
        _oso.Insert(new Fact("has_role", user, role, organization));
        
        // Set user properties
        _oso.Insert(new Fact("user_status", user, "active"));
        _oso.Insert(new Fact("joined_at", user, DateTime.UtcNow));
    }
    
    public async Task PromoteUserAsync(string userId, string organizationId, string newRole)
    {
        var user = new Value("User", userId);
        var organization = new Value("Organization", organizationId);
        
        // Remove old role and add new one
        _oso.Delete(new Fact("has_role", user, null, organization));
        _oso.Insert(new Fact("has_role", user, newRole, organization));
        
        // Log the promotion
        _oso.Insert(new Fact("role_changed", user, organization, DateTime.UtcNow));
    }
}
```

**Repository Management:**
```csharp
public class RepositoryService
{
    private readonly IOsoClient _oso;
    
    public async Task CreateRepositoryAsync(string repositoryId, string ownerId, string organizationId, bool isPublic = false)
    {
        var repository = new Value("Repository", repositoryId);
        var owner = new Value("User", ownerId);
        var organization = new Value("Organization", organizationId);
        
        // Set ownership and organization
        _oso.Insert(new Fact("owns", owner, repository));
        _oso.Insert(new Fact("belongs_to", repository, organization));
        
        // Set repository properties
        _oso.Insert(new Fact("created_at", repository, DateTime.UtcNow));
        
        if (isPublic)
        {
            _oso.Insert(new Fact("is_public", repository));
        }
        
        // Give owner admin access
        _oso.Insert(new Fact("has_role", owner, "admin", repository));
    }
    
    public async Task ShareRepositoryAsync(string repositoryId, string userId, string role)
    {
        var repository = new Value("Repository", repositoryId);
        var user = new Value("User", userId);
        
        // Grant access
        _oso.Insert(new Fact("has_role", user, role, repository));
        
        // Log sharing event
        _oso.Insert(new Fact("shared_at", repository, user, DateTime.UtcNow));
    }
}
```

## Batch Operations

Perform multiple fact operations atomically using transactions.

```csharp
oso.Batch(operations: BatchOperation[]): void
```

**Example:**
```csharp
// Atomic batch operation
var operations = new BatchOperation[]
{
    // Remove all existing roles for Alice
    new DeleteOperation(new Fact("has_role", alice, null, null)),
    
    // Add new role assignments
    new InsertOperation(new Fact("has_role", alice, "admin", repo1)),
    new InsertOperation(new Fact("has_role", alice, "member", repo2)),
    new InsertOperation(new Fact("member_of", alice, org))
};

oso.Batch(operations);
```

### Complex Operations

**User Role Migration:**
```csharp
public class RoleMigrationService
{
    private readonly IOsoClient _oso;
    
    public async Task MigrateUserRolesAsync(string userId, Dictionary<string, string> roleChanges)
    {
        var user = new Value("User", userId);
        var operations = new List<BatchOperation>();
        
        foreach (var (resourceId, newRole) in roleChanges)
        {
            var resource = new Value("Repository", resourceId);
            
            // Remove old role
            operations.Add(new DeleteOperation(new Fact("has_role", user, null, resource)));
            
            // Add new role
            operations.Add(new InsertOperation(new Fact("has_role", user, newRole, resource)));
            
            // Log the change
            operations.Add(new InsertOperation(new Fact("role_migrated", user, resource, DateTime.UtcNow)));
        }
        
        // Execute all operations atomically
        _oso.Batch(operations.ToArray());
    }
    
    public async Task TransferRepositoryOwnershipAsync(string repositoryId, string fromUserId, string toUserId)
    {
        var repository = new Value("Repository", repositoryId);
        var fromUser = new Value("User", fromUserId);
        var toUser = new Value("User", toUserId);
        
        var operations = new BatchOperation[]
        {
            // Remove old ownership
            new DeleteOperation(new Fact("owns", fromUser, repository)),
            new DeleteOperation(new Fact("has_role", fromUser, "admin", repository)),
            
            // Add new ownership
            new InsertOperation(new Fact("owns", toUser, repository)),
            new InsertOperation(new Fact("has_role", toUser, "admin", repository)),
            
            // Demote previous owner to member
            new InsertOperation(new Fact("has_role", fromUser, "member", repository)),
            
            // Log the transfer
            new InsertOperation(new Fact("ownership_transferred", repository, fromUser, toUser, DateTime.UtcNow))
        };
        
        _oso.Batch(operations);
    }
}
```

**Organization Restructuring:**
```csharp
public class OrganizationService
{
    private readonly IOsoClient _oso;
    
    public async Task MergeOrganizationsAsync(string sourceOrgId, string targetOrgId)
    {
        var sourceOrg = new Value("Organization", sourceOrgId);
        var targetOrg = new Value("Organization", targetOrgId);
        
        // Get all members and repositories from source organization
        var members = _oso.Get(new Fact("member_of", null, sourceOrg));
        var repositories = _oso.Get(new Fact("belongs_to", null, sourceOrg));
        
        var operations = new List<BatchOperation>();
        
        // Move all members to target organization
        foreach (var memberFact in members)
        {
            var user = memberFact.Args[0] as Value;
            
            operations.Add(new DeleteOperation(new Fact("member_of", user, sourceOrg)));
            operations.Add(new InsertOperation(new Fact("member_of", user, targetOrg)));
            
            // Preserve roles but update organization
            var userRoles = _oso.Get(new Fact("has_role", user, null, sourceOrg));
            foreach (var roleFact in userRoles)
            {
                var role = roleFact.Args[1] as string;
                operations.Add(new DeleteOperation(new Fact("has_role", user, role, sourceOrg)));
                operations.Add(new InsertOperation(new Fact("has_role", user, role, targetOrg)));
            }
        }
        
        // Move all repositories to target organization
        foreach (var repoFact in repositories)
        {
            var repository = repoFact.Args[0] as Value;
            
            operations.Add(new DeleteOperation(new Fact("belongs_to", repository, sourceOrg)));
            operations.Add(new InsertOperation(new Fact("belongs_to", repository, targetOrg)));
        }
        
        // Mark source organization as archived
        operations.Add(new InsertOperation(new Fact("archived", sourceOrg, DateTime.UtcNow)));
        
        _oso.Batch(operations.ToArray());
    }
}
```

## Get Facts

Retrieve facts from the authorization data store using pattern matching.

```csharp
oso.Get(pattern: Fact): List<Fact>
```

**Example:**
```csharp
// Get all roles for Alice
var aliceRoles = oso.Get(new Fact("has_role", alice, null, null));
Console.WriteLine($"Alice's roles: {aliceRoles.Count}");

// Get all members of an organization
var orgMembers = oso.Get(new Fact("member_of", null, org));
Console.WriteLine($"Organization members: {orgMembers.Count}");

// Get specific fact
var isPublic = oso.Get(new Fact("is_public", repo));
Console.WriteLine($"Repository is public: {isPublic.Count > 0}");
```

### Query Patterns

**User Profile Service:**
```csharp
public class UserProfileService
{
    private readonly IOsoClient _oso;
    
    public UserProfile GetUserProfile(string userId)
    {
        var user = new Value("User", userId);
        
        // Get all organizations user belongs to
        var organizations = _oso.Get(new Fact("member_of", user, null))
            .Select(f => f.Args[1] as Value)
            .ToList();
        
        // Get all repositories user has access to
        var repositories = _oso.Get(new Fact("has_role", user, null, null))
            .Where(f => f.Args[2] is Value v && v.Type == "Repository")
            .Select(f => new { Repository = f.Args[2] as Value, Role = f.Args[1] as string })
            .ToList();
        
        // Get user properties
        var status = _oso.Get(new Fact("user_status", user, null)).FirstOrDefault()?.Args[1] as string;
        var joinedAt = _oso.Get(new Fact("joined_at", user, null)).FirstOrDefault()?.Args[1] as DateTime?;
        
        return new UserProfile
        {
            UserId = userId,
            Organizations = organizations.Select(o => o.Id).ToList(),
            Repositories = repositories.ToDictionary(r => r.Repository.Id, r => r.Role),
            Status = status,
            JoinedAt = joinedAt
        };
    }
}

public class UserProfile
{
    public string UserId { get; set; }
    public List<string> Organizations { get; set; }
    public Dictionary<string, string> Repositories { get; set; }
    public string Status { get; set; }
    public DateTime? JoinedAt { get; set; }
}
```

**Analytics Service:**
```csharp
public class AnalyticsService
{
    private readonly IOsoClient _oso;
    
    public OrganizationStats GetOrganizationStats(string organizationId)
    {
        var organization = new Value("Organization", organizationId);
        
        // Count members
        var members = _oso.Get(new Fact("member_of", null, organization));
        
        // Count repositories
        var repositories = _oso.Get(new Fact("belongs_to", null, organization));
        
        // Count public repositories
        var publicRepos = repositories
            .Where(r => _oso.Get(new Fact("is_public", r.Args[0] as Value)).Any())
            .Count();
        
        // Get role distribution
        var roleDistribution = _oso.Get(new Fact("has_role", null, null, organization))
            .GroupBy(f => f.Args[1] as string)
            .ToDictionary(g => g.Key, g => g.Count());
        
        return new OrganizationStats
        {
            MemberCount = members.Count,
            RepositoryCount = repositories.Count,
            PublicRepositoryCount = publicRepos,
            RoleDistribution = roleDistribution
        };
    }
}

public class OrganizationStats
{
    public int MemberCount { get; set; }
    public int RepositoryCount { get; set; }
    public int PublicRepositoryCount { get; set; }
    public Dictionary<string, int> RoleDistribution { get; set; }
}
```

## Delete Facts

Remove facts from the authorization data store.

```csharp
oso.Delete(pattern: Fact): void
```

**Example:**
```csharp
// Delete specific role assignment
oso.Delete(new Fact("has_role", alice, "admin", repo));

// Delete all roles for Alice on any repository
oso.Delete(new Fact("has_role", alice, null, null));

// Delete all facts about a repository
oso.Delete(new Fact(null, null, repo));
```

### Cleanup Operations

**User Deactivation:**
```csharp
public class UserManagementService
{
    private readonly IOsoClient _oso;
    
    public async Task DeactivateUserAsync(string userId)
    {
        var user = new Value("User", userId);
        
        // Remove all role assignments
        _oso.Delete(new Fact("has_role", user, null, null));
        
        // Remove organization memberships
        _oso.Delete(new Fact("member_of", user, null));
        
        // Remove ownership (should be transferred first)
        _oso.Delete(new Fact("owns", user, null));
        
        // Mark as deactivated
        _oso.Insert(new Fact("user_status", user, "deactivated"));
        _oso.Insert(new Fact("deactivated_at", user, DateTime.UtcNow));
    }
    
    public async Task DeleteUserAsync(string userId)
    {
        var user = new Value("User", userId);
        
        // Delete all facts related to the user
        _oso.Delete(new Fact(null, user, null));  // User as subject
        _oso.Delete(new Fact(null, null, user));  // User as object
        
        // Clean up any remaining references
        var userFacts = _oso.Get(new Fact(null, null, null))
            .Where(f => f.Args.Any(arg => arg is Value v && v.Type == "User" && v.Id == userId))
            .ToList();
        
        foreach (var fact in userFacts)
        {
            _oso.Delete(fact);
        }
    }
}
```

**Repository Cleanup:**
```csharp
public class RepositoryCleanupService
{
    private readonly IOsoClient _oso;
    
    public async Task ArchiveRepositoryAsync(string repositoryId)
    {
        var repository = new Value("Repository", repositoryId);
        
        // Remove all role assignments
        _oso.Delete(new Fact("has_role", null, null, repository));
        
        // Mark as archived
        _oso.Insert(new Fact("archived", repository, DateTime.UtcNow));
        _oso.Insert(new Fact("archived_by", repository, GetCurrentUser()));
    }
    
    public async Task DeleteRepositoryAsync(string repositoryId)
    {
        var repository = new Value("Repository", repositoryId);
        
        // Delete all facts about the repository
        _oso.Delete(new Fact(null, null, repository));  // Repository as object
        _oso.Delete(new Fact(null, repository, null));  // Repository as subject
    }
    
    private Value GetCurrentUser()
    {
        // Implementation depends on your authentication setup
        throw new NotImplementedException();
    }
}
```

## Best Practices

### Fact Naming Conventions

```csharp
// Use consistent naming patterns
_oso.Insert(new Fact("has_role", user, "admin", resource));      // Role assignments
_oso.Insert(new Fact("member_of", user, organization));          // Memberships
_oso.Insert(new Fact("owns", user, resource));                   // Ownership
_oso.Insert(new Fact("belongs_to", resource, organization));     // Hierarchy
_oso.Insert(new Fact("is_public", resource));                    // Properties
_oso.Insert(new Fact("created_at", resource, timestamp));        // Timestamps
```

### Error Handling

```csharp
public class SafeFactService
{
    private readonly IOsoClient _oso;
    private readonly ILogger<SafeFactService> _logger;
    
    public async Task<bool> TryInsertFactAsync(Fact fact)
    {
        try
        {
            _oso.Insert(fact);
            _logger.LogInformation("Inserted fact: {Fact}", fact);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to insert fact: {Fact}", fact);
            return false;
        }
    }
    
    public async Task<bool> TryBatchOperationAsync(BatchOperation[] operations)
    {
        try
        {
            _oso.Batch(operations);
            _logger.LogInformation("Executed batch operation with {Count} operations", operations.Length);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute batch operation");
            return false;
        }
    }
}
```

### Validation

```csharp
public class ValidatedFactService
{
    private readonly IOsoClient _oso;
    
    public void InsertUserRole(string userId, string role, string resourceId, string resourceType)
    {
        // Validate inputs
        if (string.IsNullOrEmpty(userId)) throw new ArgumentException("User ID cannot be empty");
        if (string.IsNullOrEmpty(role)) throw new ArgumentException("Role cannot be empty");
        if (string.IsNullOrEmpty(resourceId)) throw new ArgumentException("Resource ID cannot be empty");
        
        var validRoles = new[] { "admin", "member", "viewer" };
        if (!validRoles.Contains(role))
        {
            throw new ArgumentException($"Invalid role: {role}");
        }
        
        var user = new Value("User", userId);
        var resource = new Value(resourceType, resourceId);
        
        _oso.Insert(new Fact("has_role", user, role, resource));
    }
}
```

## Next Steps

- **[Policy management](/reference/client-libraries/dotnet/policy-management)** - Deploy and manage authorization policies
- **[Local authorization](/reference/client-libraries/dotnet/local-authorization)** - Database-level filtering for performance
- **[Testing](/reference/client-libraries/dotnet/testing)** - Test your facts and authorization logic
