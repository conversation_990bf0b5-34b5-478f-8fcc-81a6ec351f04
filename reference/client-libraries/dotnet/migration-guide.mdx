---
title: Migration Guide
description: "Stable API with no breaking changes and best practices for .NET applications."
sidebarTitle: "Migration Guide"
---

## Stable API

.NET client library has a stable API with no breaking changes. The current version provides:

- **Consistent Interface**: All methods maintain backward compatibility
- **Strong Typing**: Full support for .NET type system
- **Async/Await**: Asynchronous operations where appropriate

## Best Practices

### Error Handling
```csharp
using OsoCloud.Exceptions;

public async Task<bool> CheckAuthorizationAsync(Value user, string action, Value resource)
{
    try
    {
        return oso.Authorize(user, action, resource);
    }
    catch (PolicyNotFoundException)
    {
        Console.WriteLine("No policy deployed");
        return false; // Fail closed
    }
    catch (InvalidActorException)
    {
        Console.WriteLine("Invalid user format");
        throw new ArgumentException("Authentication required");
    }
    catch (OsoException ex)
    {
        Console.WriteLine($"Authorization check failed: {ex.Message}");
        throw; // Re-throw unexpected errors
    }
}
```

### Dependency Injection (ASP.NET Core)
```csharp
// Program.cs or Startup.cs
services.AddSingleton<IOsoClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    return new OsoClient(new OsoConfig
    {
        Url = "https://cloud.osohq.com",
        ApiKey = configuration["Oso:ApiKey"]
    });
});

services.AddScoped<IAuthorizationService, AuthorizationService>();
```

## Next Steps

- **[Performance optimization](/reference/client-libraries/dotnet/performance)** - Improve authorization performance
- **[Testing strategies](/reference/client-libraries/dotnet/testing)** - Test your authorization logic
