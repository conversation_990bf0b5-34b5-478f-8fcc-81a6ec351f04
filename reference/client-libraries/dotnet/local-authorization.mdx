---
title: Local Authorization
description: "Database-level filtering with Entity Framework integration for high-performance authorization."
sidebarTitle: "Local Authorization"
---

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

## List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```csharp
oso.ListLocal(actor: Value, action: string, resourceType: string, column: string): string
```

**Basic Example:**
```csharp
using OsoCloud;

var alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
var sqlFilter = oso.ListLocal(alice, "read", "Repository", "id");
Console.WriteLine($"SQL Filter: {sqlFilter}");
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (Entity Framework):**
```csharp
using Microsoft.EntityFrameworkCore;

public async Task<List<Repository>> GetReadableRepositoriesAsync(string userId)
{
    var user = new Value("User", userId);
    var filter = oso.ListLocal(user, "read", "Repository", "Id");
    
    // Use the filter in your database query
    var repositories = await _context.Repositories
        .FromSqlRaw($"SELECT * FROM Repositories WHERE {filter}")
        .ToListAsync();
        
    return repositories;
}
```

**Advanced Usage:**
```csharp
// Get repositories Alice can admin with additional filtering
var adminFilter = oso.ListLocal(alice, "admin", "Repository", "Id");
var repositories = await _context.Repositories
    .FromSqlRaw($"SELECT * FROM Repositories WHERE {adminFilter}")
    .Where(r => r.Active == true)
    .Where(r => r.CreatedAt > new DateTime(2023, 1, 1))
    .ToListAsync();
```

## Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```csharp
oso.AuthorizeLocal(actor: Value, action: string, resource: Value, column: string): string
```

**Example:**
```csharp
var alice = new Value("User", "alice");
var repo = new Value("Repository", "acme/widgets");

// Get SQL condition for checking if Alice can read the repository
var condition = oso.AuthorizeLocal(alice, "read", repo, "Id");
Console.WriteLine($"SQL Condition: {condition}");
// Output: "Id = 'acme/widgets'"

// Use in a database query
var repository = await _context.Repositories
    .FromSqlRaw($"SELECT * FROM Repositories WHERE {condition}")
    .FirstOrDefaultAsync();
```

## Next Steps

- **[Performance tips](/reference/client-libraries/dotnet/performance)** - Optimize your authorization queries
- **[Testing](/reference/client-libraries/dotnet/testing)** - Test your local authorization logic
