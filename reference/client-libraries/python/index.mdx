---
title: Python Client Library
description: "Complete guide to using the Oso Cloud Python client library for authorization."
sidebarTitle: "Python"
---

The Oso Cloud Python client library provides comprehensive authorization capabilities with type hints, context manager support, and Pythonic patterns.

## Quick Start

```python
from oso_cloud import Oso, Value

oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"]
)

user = Value("User", "alice")
resource = Value("Repository", "acme/widgets")

# Check authorization
allowed = oso.authorize(user, "read", resource)
print(f"Access allowed: {allowed}")
```

## What's Included

<CardGroup cols={2}>
  <Card
    title="Installation & Setup"
    icon="download"
    href="/reference/client-libraries/python/installation"
  >
    Install via pip and configure your Python application
  </Card>
  
  <Card
    title="Authorization Checks"
    icon="shield-check"
    href="/reference/client-libraries/python/authorization-checks"
  >
    Core authorization methods with type hints
  </Card>
  
  <Card
    title="Facts Management"
    icon="database"
    href="/reference/client-libraries/python/facts-management"
  >
    CRUD operations with tuple-based facts
  </Card>
  
  <Card
    title="Policy Management"
    icon="file-code"
    href="/reference/client-libraries/python/policy-management"
  >
    Deploy and manage Polar policies
  </Card>
  
  <Card
    title="Local Authorization"
    icon="server"
    href="/reference/client-libraries/python/local-authorization"
  >
    SQLAlchemy integration for database filtering
  </Card>
  
  <Card
    title="Migration Guide"
    icon="arrow-right-arrow-left"
    href="/reference/client-libraries/python/migration-guide"
  >
    Upgrade from v1 to v2 with Value objects
  </Card>
</CardGroup>

## Key Features

### Type Hints
Full type hint support for better IDE integration:

```python
from typing import List
from oso_cloud import Value, Fact

def check_permissions(user: Value, resources: List[Value]) -> List[str]:
    allowed_actions: List[str] = []
    for resource in resources:
        actions = oso.actions(user, resource)
        allowed_actions.extend(actions)
    return allowed_actions
```

### Context Manager Support
Use Oso client as a context manager:

```python
with Oso(url="https://cloud.osohq.com", api_key=api_key) as oso:
    result = oso.authorize(user, "read", resource)
    print(f"Authorized: {result}")
```

## Framework Integration

### Django
```python
# middleware.py
class OsoAuthorizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        if hasattr(request, 'user') and request.user.is_authenticated:
            user = Value("User", str(request.user.id))
            # Add authorization logic
        return self.get_response(request)
```

### Flask
```python
from flask import g, request, abort

@app.before_request
def check_authorization():
    if request.endpoint and hasattr(g, 'user'):
        user = Value("User", g.user.id)
        # Add authorization logic
```

## Next Steps

1. **[Install the package](/reference/client-libraries/python/installation)** - Get started quickly
2. **[Learn core concepts](/reference/client-libraries/python/authorization-checks)** - Master authorization
3. **[See examples](https://github.com/osohq/oso-cloud-python-examples)** - Real-world implementations
