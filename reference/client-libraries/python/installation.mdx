---
title: Installation & Setup
description: "Install and configure the Oso Cloud Python client library in your application."
sidebarTitle: "Installation"
---

## Installation

Install the Oso Cloud Python client:

```bash
pip install oso-cloud
```

**Requirements:**
- Python 3.8 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```python
from oso_cloud import Oso

oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"]
)
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```python
oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"],
    timeout=30.0,  # Request timeout in seconds
    retries=3      # Number of retry attempts
)
```

## Framework Integration

### Django

**Settings Configuration:**
```python
# settings.py
import os
from oso_cloud import Oso

OSO_CLIENT = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"]
)

# Add to INSTALLED_APPS if using Django integration
INSTALLED_APPS = [
    # ... other apps
    'oso_django',  # If using Django integration package
]
```

**Middleware:**
```python
# middleware.py
from django.http import HttpResponseForbidden
from django.conf import settings
from oso_cloud import Value

class OsoAuthorizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.oso = settings.OSO_CLIENT
        
    def __call__(self, request):
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Add authorization logic here
            pass
        return self.get_response(request)
```

**View Decorators:**
```python
# decorators.py
from functools import wraps
from django.http import HttpResponseForbidden
from django.conf import settings
from oso_cloud import Value

def require_permission(action, resource_type=None):
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return HttpResponseForbidden("Authentication required")
            
            user = Value("User", str(request.user.id))
            
            if resource_type:
                resource_id = kwargs.get('id') or kwargs.get('pk')
                resource = Value(resource_type, resource_id)
                
                if not settings.OSO_CLIENT.authorize(user, action, resource):
                    return HttpResponseForbidden("Access denied")
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

# Usage
@require_permission("read", "Repository")
def repository_detail(request, id):
    # View logic
    pass
```

### Flask

**Application Setup:**
```python
# app.py
from flask import Flask, g, request, abort
from oso_cloud import Oso, Value
import os

app = Flask(__name__)

# Initialize Oso client
oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"]
)

@app.before_request
def load_user():
    # Load user from session/token
    g.user_id = get_current_user_id()  # Your auth logic

def require_permission(action, resource_type=None):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not g.user_id:
                abort(401, "Authentication required")
            
            user = Value("User", g.user_id)
            
            if resource_type:
                resource_id = kwargs.get('id')
                resource = Value(resource_type, resource_id)
                
                if not oso.authorize(user, action, resource):
                    abort(403, "Access denied")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Usage
@app.route('/repositories/<id>')
@require_permission("read", "Repository")
def get_repository(id):
    # Route logic
    pass
```

### FastAPI

**Application Setup:**
```python
# main.py
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from oso_cloud import Oso, Value
import os

app = FastAPI()
security = HTTPBearer()

# Initialize Oso client
oso = Oso(
    url="https://cloud.osohq.com",
    api_key=os.environ["OSO_AUTH"]
)

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    # Your authentication logic
    user_id = verify_token(credentials.credentials)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    return Value("User", user_id)

def require_permission(action: str, resource_type: str = None):
    async def permission_checker(
        user: Value = Depends(get_current_user),
        resource_id: str = None
    ):
        if resource_type and resource_id:
            resource = Value(resource_type, resource_id)
            if not oso.authorize(user, action, resource):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        return user
    return permission_checker

# Usage
@app.get("/repositories/{repository_id}")
async def get_repository(
    repository_id: str,
    user: Value = Depends(require_permission("read", "Repository"))
):
    # Route logic
    pass
```

## Context Manager Support

Use Oso client as a context manager for automatic cleanup:

```python
with Oso(url="https://cloud.osohq.com", api_key=api_key) as oso:
    result = oso.authorize(user, "read", resource)
    print(f"Authorized: {result}")
```

## Type Hints

Full type hint support for better IDE integration:

```python
from typing import List
from oso_cloud import Value, Fact

def check_permissions(user: Value, resources: List[Value]) -> List[str]:
    allowed_actions: List[str] = []
    for resource in resources:
        actions = oso.actions(user, resource)
        allowed_actions.extend(actions)
    return allowed_actions
```

## Environment Configuration

### Development
```python
# config/development.py
import os
from oso_cloud import Oso

OSO_CONFIG = {
    'url': 'https://cloud.osohq.com',
    'api_key': os.environ.get('OSO_AUTH_DEV'),
    'timeout': 30.0,
    'retries': 3
}

oso = Oso(**OSO_CONFIG)
```

### Production
```python
# config/production.py
import os
from oso_cloud import Oso

OSO_CONFIG = {
    'url': 'https://cloud.osohq.com',
    'api_key': os.environ['OSO_AUTH'],  # Required in production
    'timeout': 10.0,  # Shorter timeout for production
    'retries': 5      # More retries for production
}

oso = Oso(**OSO_CONFIG)
```

### Docker
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV OSO_URL=https://cloud.osohq.com

EXPOSE 8000
CMD ["python", "app.py"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OSO_AUTH=${OSO_AUTH}
      - OSO_URL=https://cloud.osohq.com
    env_file:
      - .env
```

## Testing Setup

```python
# conftest.py
import pytest
from unittest.mock import Mock
from oso_cloud import Oso, Value

@pytest.fixture
def mock_oso():
    """Mock Oso client for testing."""
    mock = Mock(spec=Oso)
    mock.authorize.return_value = True
    mock.list.return_value = []
    mock.actions.return_value = []
    mock.insert.return_value = None
    mock.delete.return_value = None
    mock.get.return_value = []
    mock.batch.return_value = None
    mock.policy.return_value = None
    mock.get_policy.return_value = ""
    mock.stats.return_value = {"num_rules": 0, "num_types": 0, "num_resources": 0}
    return mock

@pytest.fixture
def test_user():
    """Create a test user Value."""
    return Value("User", "test-user")

@pytest.fixture
def test_repository():
    """Create a test repository Value."""
    return Value("Repository", "test-repo")
```

## Next Steps

- **[Learn authorization basics](/reference/client-libraries/python/authorization-checks)** - Master the core authorization methods
- **[Manage facts](/reference/client-libraries/python/facts-management)** - Store and retrieve authorization data
- **[Deploy policies](/reference/client-libraries/python/policy-management)** - Manage your authorization policies
