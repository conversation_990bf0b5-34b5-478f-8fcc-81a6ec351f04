---
title: Java Client Library
description: "Complete guide to using the Oso Cloud Java client library for authorization."
sidebarTitle: "Java"
---

## Installation

Add the Oso Cloud Java client to your project:

**Maven:**
```xml
<dependency>
    <groupId>com.osohq</groupId>
    <artifactId>oso-cloud</artifactId>
    <version>1.0.0</version>
</dependency>
```

**Gradle:**
```groovy
implementation 'com.osohq:oso-cloud:1.0.0'
```

**Requirements:**
- Java 11 or later

## Basic Setup

Initialize the Oso Cloud client with your API key:

```java
import com.osohq.oso_cloud.*;

public class Main {
    public static void main(String[] args) {
        OsoCloud oso = new OsoCloud.Builder()
            .url("https://cloud.osohq.com")
            .apiKey(System.getenv("OSO_AUTH"))
            .build();
    }
}
```

**Environment Variables:**
```bash
export OSO_AUTH="your-api-key-here"
```

**Configuration Options:**
```java
OsoCloud oso = new OsoCloud.Builder()
    .url("https://cloud.osohq.com")
    .apiKey(System.getenv("OSO_AUTH"))
    .timeout(30000)  // Request timeout in milliseconds
    .retries(3)      // Number of retry attempts
    .build();
```

## Authorization Checks

Authorization checks are the core of Oso Cloud - they determine whether a user can perform a specific action on a resource.

### Basic Authorization

Determines whether an actor is authorized to perform an action on a resource.

```java
oso.authorize(actor: Value, action: String, resource: Value, contextFacts: Fact[]): boolean throws ApiException
```

**Basic Example:**
```java
import com.osohq.oso_cloud.*;

Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Check if Alice can read the repository
boolean canRead = oso.authorize(alice, "read", repo);
System.out.println("Alice can read repo: " + canRead);

// Check if Alice can write to the repository
boolean canWrite = oso.authorize(alice, "write", repo);
System.out.println("Alice can write to repo: " + canWrite);
```

**With Context Facts:**
```java
// Check authorization with temporary context
Fact[] contextFacts = {new Fact("is_public", repo)};
boolean canReadPublic = oso.authorize(alice, "read", repo, contextFacts);
```

### List Resources

Fetches all resources of a given type that an actor can perform a specific action on.

```java
oso.list(actor: Value, action: String, resourceType: String, contextFacts: Fact[]): String[] throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");

// Get all repositories Alice can read
String[] readableRepos = oso.list(alice, "read", "Repository");
System.out.println("Readable repositories: " + Arrays.toString(readableRepos));

// Get all organizations Alice can admin
String[] adminOrgs = oso.list(alice, "admin", "Organization");
System.out.println("Admin organizations: " + Arrays.toString(adminOrgs));
```

**With Context Facts:**
```java
// List with additional context
Fact[] contextFacts = {new Fact("user_preference", alice, new Value("show_public"), new Value(true))};
String[] publicRepos = oso.list(alice, "read", "Repository", contextFacts);
```

### List Actions

Fetches all actions that an actor can perform on a specific resource.

```java
oso.actions(actor: Value, resource: Value, contextFacts: Fact[]): String[] throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Get all actions Alice can perform on the repository
String[] allowedActions = oso.actions(alice, repo);
System.out.println("Allowed actions: " + Arrays.toString(allowedActions));
// Output: ["read", "write"] or ["read", "write", "admin"]
```

### Advanced Querying

For complex authorization queries, use the Query Builder API to construct sophisticated queries with multiple conditions.

```java
oso.buildQuery(predicate: String, ...args: Value): QueryBuilder
```

**Basic Query:**
```java
// Find all users who can admin a specific repository
String[] adminUsers = oso.buildQuery("allow", oso.var("user"), "admin", repo)
                        .evaluate(EvaluateArgs.values(oso.var("user")));

System.out.println("Admin users: " + Arrays.toString(adminUsers));
```

**Complex Query with Conditions:**
```java
// Find all repositories in the "acme" organization that Alice can read
Value acmeOrg = new Value("Organization", "acme");
String[] readableAcmeRepos = oso.buildQuery("allow", alice, "read", oso.var("repo"))
                               .and("parent", oso.var("repo"), acmeOrg)
                               .evaluate(EvaluateArgs.values(oso.var("repo")));

System.out.println("Readable acme repositories: " + Arrays.toString(readableAcmeRepos));
```

**Advanced Evaluation Options:**
```java
// Evaluate to get detailed results with nested maps
Map<String, Object>[] detailedResults = oso.buildQuery("allow", oso.var("user"), "read", oso.var("repo"))
                                          .evaluate(EvaluateArgs.nested(
                                              Map.of("user", oso.var("user"), "repo", oso.var("repo"))
                                          ));
```

**Migration from v0:**
```java
// Old (v0)
QueryResult[] results = oso.query("allow", alice, "read", oso.var("repo"));

// New (v1)
String[] results = oso.buildQuery("allow", alice, "read", oso.var("repo"))
                     .evaluate(EvaluateArgs.values(oso.var("repo")));
```

## Facts Management

Facts are the authorization data stored in Oso Cloud that your policies use to make authorization decisions.

### Insert Facts

Adds a single fact to the centralized authorization data store.

```java
oso.insert(fact: Fact): void throws ApiException
```

**Example:**
```java
import com.osohq.oso_cloud.*;

// Create entity values
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");
Value org = new Value("Organization", "acme");

// Insert role assignment
oso.insert(new Fact("has_role", alice, "admin", repo));

// Insert organization membership
oso.insert(new Fact("member_of", alice, org));

// Insert resource property
oso.insert(new Fact("is_public", repo));
```

**Migration from v0:**
```java
// Old (v0)
oso.tell("has_role", alice, "admin", repo);

// New (v1)
oso.insert(new Fact("has_role", alice, "admin", repo));
```

### Batch Operations

Perform multiple fact operations atomically using transactions.

```java
oso.batch(operations: BatchOperation[]): void throws ApiException
```

**Example:**
```java
// Atomic batch operation
BatchOperation[] operations = {
    // Remove all existing roles for Alice
    new DeleteOperation(new Fact("has_role", alice, null, null)),
    
    // Add new role assignments
    new InsertOperation(new Fact("has_role", alice, "admin", repo1)),
    new InsertOperation(new Fact("has_role", alice, "member", repo2)),
    new InsertOperation(new Fact("member_of", alice, org))
};

oso.batch(operations);
```

### Get Facts

Retrieve facts from the authorization data store using pattern matching.

```java
oso.get(pattern: Fact): Fact[] throws ApiException
```

**Example:**
```java
// Get all roles for Alice
Fact[] aliceRoles = oso.get(new Fact("has_role", alice, null, null));
System.out.println("Alice's roles: " + Arrays.toString(aliceRoles));

// Get all members of an organization
Fact[] orgMembers = oso.get(new Fact("member_of", null, org));
System.out.println("Organization members: " + Arrays.toString(orgMembers));

// Get specific fact
Fact[] isPublic = oso.get(new Fact("is_public", repo));
System.out.println("Repository is public: " + (isPublic.length > 0));
```

### Delete Facts

Remove facts from the authorization data store.

```java
oso.delete(pattern: Fact): void throws ApiException
```

**Example:**
```java
// Delete specific role assignment
oso.delete(new Fact("has_role", alice, "admin", repo));

// Delete all roles for Alice on any repository
oso.delete(new Fact("has_role", alice, null, null));

// Delete all facts about a repository
oso.delete(new Fact(null, null, repo));
```

## Policy Management

Deploy and manage your Polar authorization policies in Oso Cloud.

### Deploy Policy

Updates the authorization policy in Oso Cloud. The policy is validated and tested before deployment.

```java
oso.policy(policy: String): void throws ApiException
```

**Example:**
```java
try {
    String policy = """
        actor User {}
        resource Repository {
            permissions = ["read", "write", "admin"];
            roles = ["member", "maintainer", "admin"];
            
            "member" if "maintainer";
            "maintainer" if "admin";
        }
        
        allow(user: User, "read", repo: Repository) if
            has_role(user, "member", repo);
            
        allow(user: User, "write", repo: Repository) if
            has_role(user, "maintainer", repo);
            
        allow(user: User, "admin", repo: Repository) if
            has_role(user, "admin", repo);
            
        test "basic permissions" {
            setup {
                has_role(User{"alice"}, "member", Repository{"widgets"});
            }
            
            assert allow(User{"alice"}, "read", Repository{"widgets"});
            assert_not allow(User{"alice"}, "write", Repository{"widgets"});
        }
    """;
    
    oso.policy(policy);
    System.out.println("Policy deployed successfully!");
} catch (ApiException e) {
    System.err.println("Policy deployment failed: " + e.getMessage());
}
```

### Get Policy

Retrieve the currently deployed policy.

```java
oso.getPolicy(): String throws ApiException
```

**Example:**
```java
String currentPolicy = oso.getPolicy();
System.out.println("Current policy: " + currentPolicy);
```

### Policy Stats

Get statistics about your deployed policy.

```java
oso.stats(): PolicyStats throws ApiException
```

**Example:**
```java
PolicyStats stats = oso.stats();
System.out.println("Policy statistics: " +
    "NumRules=" + stats.getNumRules() + 
    ", NumTypes=" + stats.getNumTypes() + 
    ", NumResources=" + stats.getNumResources());
```

## Local Authorization

Local authorization allows you to enforce authorization at the database level by generating SQL queries that filter results based on your Oso Cloud policies.

### List Local

Generates a SQL filter to return all resources of a given type that an actor can perform an action on.

```java
oso.listLocal(actor: Value, action: String, resourceType: String, column: String): String throws ApiException
```

**Basic Example:**
```java
import com.osohq.oso_cloud.*;

Value alice = new Value("User", "alice");

// Get SQL filter for repositories Alice can read
String sqlFilter = oso.listLocal(alice, "read", "Repository", "id");
System.out.println("SQL Filter: " + sqlFilter);
// Output: "id IN ('repo1', 'repo2', 'repo3')"
```

**Database Integration (JDBC):**
```java
import java.sql.*;

public List<Repository> getReadableRepositories(String userId) throws SQLException, ApiException {
    Value user = new Value("User", userId);
    String filter = oso.listLocal(user, "read", "Repository", "id");
    
    String query = "SELECT * FROM repositories WHERE " + filter;
    
    List<Repository> repositories = new ArrayList<>();
    try (PreparedStatement stmt = connection.prepareStatement(query);
         ResultSet rs = stmt.executeQuery()) {
        
        while (rs.next()) {
            repositories.add(new Repository(rs.getString("id"), rs.getString("name")));
        }
    }
    
    return repositories;
}
```

**Advanced Usage:**
```java
// Get repositories Alice can admin with additional filtering
String adminFilter = oso.listLocal(alice, "admin", "Repository", "id");
String query = "SELECT * FROM repositories WHERE " + adminFilter + 
               " AND active = true AND created_at > '2023-01-01'";

try (PreparedStatement stmt = connection.prepareStatement(query);
     ResultSet rs = stmt.executeQuery()) {
    // Process results
}
```

### Authorize Local

Generates a SQL condition to check if an actor can perform an action on a specific resource.

```java
oso.authorizeLocal(actor: Value, action: String, resource: Value, column: String): String throws ApiException
```

**Example:**
```java
Value alice = new Value("User", "alice");
Value repo = new Value("Repository", "acme/widgets");

// Get SQL condition for checking if Alice can read the repository
String condition = oso.authorizeLocal(alice, "read", repo, "id");
System.out.println("SQL Condition: " + condition);
// Output: "id = 'acme/widgets'"

// Use in a database query
String query = "SELECT * FROM repositories WHERE " + condition;
try (PreparedStatement stmt = connection.prepareStatement(query);
     ResultSet rs = stmt.executeQuery()) {
    
    if (rs.next()) {
        Repository repository = new Repository(rs.getString("id"), rs.getString("name"));
        return repository;
    }
}
```

## Migration Guide

### Breaking Changes (v0 → v1)

#### Package Consolidation
All classes are now in a single package:

```java
// Old (v0)
import com.osohq.oso_cloud.client.OsoCloud;
import com.osohq.oso_cloud.model.Value;

// New (v1)
import com.osohq.oso_cloud.*;
```

#### ValuePattern Introduction
Use `ValuePattern` for wildcard matching in facts:

```java
// Old (v0)
oso.get("has_role", alice, null, null);

// New (v1)
oso.get(new Fact("has_role", alice, ValuePattern.any(), ValuePattern.any()));
```

#### Sophisticated QueryBuilder
Enhanced query building with fluent interface:

```java
// Old (v0)
QueryResult[] results = oso.query("allow", alice, "read", oso.var("repo"));

// New (v1)
String[] results = oso.buildQuery("allow", alice, "read", oso.var("repo"))
                     .and("parent", oso.var("repo"), organization)
                     .evaluate(EvaluateArgs.values(oso.var("repo")));
```

### New Features

#### Exception Handling
Specific exception types for different error scenarios:

```java
import com.osohq.oso_cloud.exceptions.*;

try {
    boolean allowed = oso.authorize(user, action, resource);
    return allowed;
} catch (PolicyNotFoundException e) {
    System.out.println("No policy deployed");
    return false; // Fail closed
} catch (InvalidActorException e) {
    System.out.println("Invalid user format");
    throw new IllegalArgumentException("Authentication required");
} catch (ApiException e) {
    System.err.println("Authorization check failed: " + e.getMessage());
    throw e; // Re-throw unexpected errors
}
```

#### Builder Pattern
Fluent configuration with builder pattern:

```java
OsoCloud oso = new OsoCloud.Builder()
    .url("https://cloud.osohq.com")
    .apiKey(System.getenv("OSO_AUTH"))
    .timeout(30000)
    .retries(3)
    .enableLogging(true)
    .build();
```

## Error Handling

```java
import com.osohq.oso_cloud.exceptions.*;

public boolean checkAuthorization(Value user, String action, Value resource) {
    try {
        return oso.authorize(user, action, resource);
    } catch (PolicyNotFoundException e) {
        logger.warn("No policy deployed");
        return false; // Fail closed
    } catch (InvalidActorException e) {
        logger.error("Invalid user format: {}", e.getMessage());
        throw new IllegalArgumentException("Authentication required", e);
    } catch (TimeoutException e) {
        logger.error("Authorization check timed out: {}", e.getMessage());
        throw new RuntimeException("Authorization service unavailable", e);
    } catch (ApiException e) {
        logger.error("Authorization check failed: {}", e.getMessage());
        throw new RuntimeException("Authorization check failed", e);
    }
}
```

## Performance Considerations

### Connection Pooling
```java
// Configure HTTP client with connection pooling
OkHttpClient httpClient = new OkHttpClient.Builder()
    .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
    .readTimeout(30, TimeUnit.SECONDS)
    .build();

OsoCloud oso = new OsoCloud.Builder()
    .url("https://cloud.osohq.com")
    .apiKey(System.getenv("OSO_AUTH"))
    .httpClient(httpClient)
    .build();
```

### Batch Operations
```java
// Instead of multiple individual checks
boolean canRead = oso.authorize(alice, "read", repo1);
boolean canWrite = oso.authorize(alice, "write", repo1);
boolean canAdmin = oso.authorize(alice, "admin", repo1);

// Use actions() for multiple permission checks on same resource
String[] allowedActions = oso.actions(alice, repo1);
boolean canRead = Arrays.asList(allowedActions).contains("read");
boolean canWrite = Arrays.asList(allowedActions).contains("write");
boolean canAdmin = Arrays.asList(allowedActions).contains("admin");
```

### Caching
```java
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

public class CachedAuthorizationService {
    private final Cache<String, Boolean> authCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();
    
    public boolean authorize(Value user, String action, Value resource) {
        String key = user.getId() + ":" + action + ":" + resource.getId();
        
        Boolean cached = authCache.getIfPresent(key);
        if (cached != null) {
            return cached;
        }
        
        boolean result = oso.authorize(user, action, resource);
        authCache.put(key, result);
        return result;
    }
}
```

## Next Steps

- [Policy Patterns](/develop/policies/patterns) - Learn common authorization patterns
- [Performance Guide](/develop/troubleshooting/query-performance) - Optimize authorization performance
- [HTTP API Reference](/reference/api/check-api) - Direct API access
